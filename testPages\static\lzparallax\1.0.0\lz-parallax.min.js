!function (factory) {
    // AMD模块化加载
    if (typeof define === "function" && define.amd) {
        define(["jquery"], factory);
    } else {
        factory(jQuery);
    }
}(function ($) {

    "use strict";

    var lzparallax = {

        name: '视察滚动',
        version: '1.5.7',
        author: 'lzx',
        date: '2017-12-13'
    }


    $.extend({
        isLzString: function (v) {
            return Object.prototype.toString.call(v) === "[object String]";
        },
        isLzObject: function (v) {
            return Object.prototype.toString.call(v) === "[object Object]";
        },
        isLzNumber: function (v) {
            return Object.prototype.toString.call(v) === "[object Number]";
        },
        isLzWindow: function (v) {
            return Object.prototype.toString.call(v) === "[object Window]";
        },
        isLzBrowser: function () {
            // 检测内核
            var ua = navigator.userAgent, tem,
                M = ua.match(/(opera|chrome|safari|firefox|msie|trident(?=\/))\/?\s*(\d+)/i) || [];
            if (/trident/i.test(M[1])) {
                tem = /\brv[ :]+(\d+)/g.exec(ua) || [];
                return ['IE ', (tem[1] || '')];
            }
            if (M[1] === 'Chrome') {
                tem = ua.match(/\b(OPR|Edge)\/(\d+)/);
                if (tem != null) return tem.slice(1);
            }
            M = M[2] ? [M[1], M[2]] : [navigator.appName, navigator.appVersion, '-?'];
            if ((tem = ua.match(/version\/(\d+)/i)) != null) M.splice(1, 1, tem[1]);
            return M;
        },
        // 检查平台
        isPC: function () {
            var userAgentInfo = navigator.userAgent,
                Agents = new Array("Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod"),
                flag = true;
            for (var v = 0; v < Agents.length; v++) {
                if (userAgentInfo.indexOf(Agents[v]) > 0) { flag = false; break; }
            }
            return flag;
        }
    });

    var parallaxEvent = function (val) {

        var me = this;

        me.$ele = $(val);

        me.param = me.$ele.data('lzparallax-param');

        me.status = me.$ele.data('lzparallax-status');

        me.methods = me.$ele.data('lzparallaxMethods');
    }


    parallaxEvent.prototype = {

        constructor: parallaxEvent,

        setEvent: function () {

            var event = this,

                param = this.param;

            $.each(param.event, function (index, val) {

                event[val] && event[val]();

            })

        },

        scroll: function () {
            var _event = this,
                _me = this.methods;
            _me.$scrollBox.on('scroll.lzParallax', function () {
                _me._movePosition();
            });
        },

        mouseEnter: function () {
            var _event = this,
                _me = _event.methods,
                param = _event.param,
                $this = _event.$ele;

            _me.$ele.off('mouseenter.lzparallax').on('mouseenter.lzparallax', function () {
                $this.children('.lz-parallax-bg').find('.lz-parallax-bg-img').addClass(param.hoverClass);
            }).off('mouseleave.lzparallax').on('mouseleave.lzparallax', function () {
                $this.children('.lz-parallax-bg').find('.lz-parallax-bg-img').removeClass(param.hoverClass);
            });
        },

        resize: function () {
            var _event = this,
                _me = _event.methods,
                param = _event.param,
                status = _event.status,
                $this = _event.$ele;
            $(window).resize(function () {
                _me._getWindowSize();
                _me._getBoxSize();
                _me._getBoxPosition();
                _me.$ele.children('.lz-parallax-bg').find('.lz-parallax-bg-inner').css({
                    left: status.boxPosition[0],
                    width: status.boxSize[0]
                });
                _me._movePosition();
            });
        }
    }

    var parallaxMethods = function (val) {

        var _me = this;

        _me.$ele = $(val);

        _me.param = _me.$ele.data("lzparallax-param");

        _me.status = _me.$ele.data("lzparallax-status");

        _me.lzparallax = _me.$ele.data('lzparallax');

        _me.lzparallax.$dom = _me.$ele;
    }

    parallaxMethods.prototype = {

        constructor: parallaxMethods,

        _clone: function (me) {
            var _me = this,
                $this = _me.$ele,
                param = _me.param,
                init = _me.lzparallax;

            init.$dom && ($this = _me.$ele = init.$dom);
            if ($.isLzObject(param)) {
                if (param.clone) {
                    var dom = "<div class='lz-parallax-bg_clone'></div>";
                    $this.children('.lz-parallax-bg_clone').remove();
                    $this.prepend(dom);
                    $this.css('position') == 'static' && $this.css('position', 'relative');
                    var $clone = $this.children('.lz-parallax-bg_clone');
                    $clone.css($this.css([
                        'backgroundImage',
                        'backgroundPosition',
                        'backgroundRepeat',
                        'backgroundSize',
                        'backgroundColor',
                        'margin',
                        'padding',
                        'minWidth',
                        'maxWidth',
                        'minHeight',
                        'maxHeight'])).css({
                            'position': 'absolute',
                            'left': 0,
                            'right': 0,
                            'top': '-1px',
                            'bottom': '-1px',
                            'zIndex': '0',
                            'pointer-events': 'none'
                        });
                    $this.css('background', 'none');
                    _me.$ele = init.$ele = $clone;

                    _me.$ele.data({
                        'lzparallax-param': _me.param,
                        'lzparallax-status': _me.status,
                        'lzparallax': _me.lzparallax,
                        'lzparallaxMethods': this
                    });
                }
            }
        },

        _getWindowSize: function () {
            var status = this.status;
            status.windowSize = [
                $(window).width(),
                $(window).height()
            ];
        },

        _findScroll: function () {
            var _me = this,
                $scrollBox = _me.$ele.parent();
            while (true) {
                if ($scrollBox.css('overflow') == 'auto' || $scrollBox.css('overflow') == 'scroll' || $scrollBox.children('.lz-scrollBar').size() !== 0 || $scrollBox[0].tagName === 'BODY') {
                    break;
                } else {
                    $scrollBox = $scrollBox.parent();
                }
            }
            _me.$scrollBox = $scrollBox[0].tagName === 'BODY' ? $(window) : $scrollBox;
        },

        _cleartransform: function () {
            var _me = this,
                $transformDom = _me.$ele;
            _me.$ele[0].transformDom = [];
            while (true) {
                if ($transformDom[0].tagName === 'BODY') {
                    break;
                } else {
                    $transformDom = $transformDom.parent();
                    if ($transformDom.css('transform') != 'none') {
                        _me.$ele[0].transformDom.push({ 'dom': $transformDom, 'val': $transformDom.css('transform') })
                        $transformDom.css({ 'transform': 'none' });
                    }
                }
            }
        },

        _getBoxSize: function () {
            var _me = this;
            _me.status.boxSize = [_me.$ele.outerWidth(), _me.$ele.outerHeight()];
        },

        _getBoxPosition: function () {
            var _me = this,
                status = _me.status,
                autoPosition = _me.param.autoPosition;
            autoPosition == null && (autoPosition = true);
            status.boxPosition = [
                autoPosition ? _me.$ele.offset().left - _me.$scrollBox.scrollLeft() : '',
                _me.$ele.offset().top - _me.$scrollBox.scrollTop()
            ];
        },

        _getImgHeight: function () {
            var _me = this,
                status = _me.status,
                scrollBox = _me.$scrollBox[0],
                param = _me.param;
            if (param.effect == 'scroll') {
                _me.$ele.children('.lz-parallax-bg').find('.lz-parallax-bg-img').css('height', '120%');
            } else {
                _me.$ele.children('.lz-parallax-bg').find('.lz-parallax-bg-img').css({
                    'height': '100%',
                    'top': 0
                });
            }
        },

        _displayImage: function () {
            var _me = this,
                status = _me.status,
                param = _me.param;
            status.stopEvent === undefined && (status.stopEvent = param.stopEvent);
            if (status.stopEvent === true) {
                _me.$ele.children('.lz-parallax-bg').css({
                    'clip': '',
                    'overflow': 'hidden'
                });
                _me.$ele.children('.lz-parallax-bg').find('.lz-parallax-bg-inner').css({
                    'position': 'absolute',
                    'height': status.windowSize[1],
                    'top': -status.boxPosition[1]
                });
                status.imgMin = true;

                var offsetTop = _me.$ele.offset().top;

                _me.$scrollBox.on('scroll', function () {
                    _me.$ele.children('.lz-parallax-bg').find('.lz-parallax-bg-inner').css({ 'top': -(offsetTop - _me.$scrollBox.scrollTop()) });
                })
            }

        },

        _setDisplayImagePosition: function () {
            var _me = this,
                status = _me.status,
                param = _me.param;
            _me.$ele.children('.lz-parallax-bg').find('.lz-parallax-bg-inner').css({ 'top': -status.boxPosition[1] });
        },

        _getMoveSize: function () {
            var _me = this,
                status = _me.status,
                param = _me.param;
            if (param.effect == 'fixed') {
                return false;
            }
            var scrollPosition = $(document).height() - (_me.$scrollBox.scrollTop() + status.windowSize[1]);
            if (status.initPositionFlag) {
                if (status.boxPosition[1] < status.windowSize[1] && status.boxPosition[1] + status.boxSize[1] < scrollPosition) {
                    status.pageScrollHeight = status.boxPosition[1] + status.boxSize[1];
                }
                if (status.boxPosition[1] + status.boxSize[1] >= scrollPosition) {
                    status.pageScrollHeight = scrollPosition;
                }
                if (status.boxPosition[1] > status.windowSize[1] && status.boxPosition[1] + status.boxSize[1] < scrollPosition) {
                    status.pageScrollHeight = status.windowSize[1] + status.boxSize[1];
                }
                status.initPosition = status.boxPosition[1];
            }
            status.goMove = ((status.windowSize[1] * (20 / 100)) / status.pageScrollHeight) * (status.initPosition - status.boxPosition[1]);
            status.initPositionFlag && (status.goMove = 0);
        },
        _movePosition: function (skipBrowser) {
            skipBrowser = skipBrowser === undefined ? "Safari" : skipBrowser;
            var _me = this,
                param = _me.param,
                status = _me.status;
            _me._getBoxPosition();
            _me._getMoveSize();
            $.isLzBrowser()[0] != skipBrowser && _me.$ele.children('.lz-parallax-bg').css({ 'clip': 'rect(0 ' + status.boxSize[0] + 'px ' + status.boxSize[1] + 'px 0)', 'overflow': '' });
            if (param.effect == 'scroll' && status.boxPosition[1] <= status.windowSize[1] && status.boxPosition[1] >= -status.boxSize[1]) {
                _me.$ele.children('.lz-parallax-bg').find('.lz-parallax-bg-img').css('transform', 'translate(0px,' + -status.goMove + 'px');
                status.initPositionFlag = false;
            }
            _me.$ele.hide().show(0);//强制刷新样式
        },

        _saveBg: function (obj) {
            var _me = this,
                status = _me.status;
            if (!status.bgVal['backgroundImage']) {
                status.bgVal = {
                    backgroundImage: _me.$ele.css('backgroundImage'),
                    backgroundPosition: _me.$ele.css('backgroundPosition'),
                    backgroundRepeat: _me.$ele.css('backgroundRepeat'),
                    backgroundSize: _me.$ele.css('backgroundSize'),
                    backgroundColor: _me.$ele.css('backgroundColor')
                }
                _me.$ele.css('backgroundImage', 'none');
            }

        },

        _layout: function () {
            var _me = this,
                status = _me.$ele.data('lzparallax-status'),
                dom = "<div class='lz-parallax-content' style='position:relative;top:0;left:0;width:100%;height:100%;z-index:10'></div>"
                    + "<div class='lz-parallax-bg' style='position:absolute;top:0;left:0;right:0;bottom:0;-webkit-clip-path: border-box;overflow:hidden'>"
                    + "<div class='lz-parallax-bg-inner' style='position:fixed;top:0;left:" + status.boxPosition[0] + "px;width:" + status.boxSize[0] + "px;bottom:0;z-index:-1;pointer-events:none'>"
                    + "<div class='lz-parallax-bg-img' style='position:absolute;width:100%;pointer-events:none'></div>"
                    + "</div>"
                    + "</div>";
            _me.$ele.children('.lz-parallax-content,.lz-parallax-bg').remove();
            var $innerHtml = _me.$ele.children();
            _me.$ele.html(dom);
            _me.$ele.css('position') == 'static' && _me.$ele.css('position', 'relative');
            _me.$ele.children('.lz-parallax-content').prepend($innerHtml);
            _me.$ele.children('.lz-parallax-bg').find('.lz-parallax-bg-img').css(status.bgVal);
        },
    }

    var parallaxInit = function (val) {

        var _me = this;

        _me.$ele = $(val);

        _me.$ele.data('lzparallax-status', {
            bgVal: [],
            boxPosition: [],
            windowSize: [],
            boxSize: [],
            initPosition: 0,
            bgTop: 0,
            goToS: 0,
            initPositionFlag: true,
        });
        _me.status = _me.$ele.data('lzparallax-status');
    }

    parallaxInit.prototype = {

        constructor: parallaxInit,

        defaults: {
            hoverClass: '',//效果一般，不推荐使用
            effect: 'scroll',
            autoPosition: false,
            clone: false,
            stopEvent: false,
            event: ['scroll', 'mouseEnter', 'resize']
        },

        _initParam: function (a) {

            var param = this.defaults,

                temp = null;

            if ($.isLzObject(a)) {
                param = $.extend({}, param, a);
            }

            return param;
        },

        _initLayout: function () {
            var _me = this,
                param = _me.$ele.data('lzparallax-param'),
                methods = _me.$ele.data('lzparallaxMethods'),
                currentScroll;
            // pc 使用background-attachment: fixed
            if ($.isPC() && param.effect == 'fixed') {
                _me.$ele.css('background-attachment', 'fixed')
                param.stopEvent = true;
                return;
            }
            if (param.effect == 'fixed' || param.effect == 'scroll') {
                _me.status.initPositionFlag = true;
                methods._clone();
                methods._findScroll();
                methods._cleartransform();
                methods._getWindowSize();
                methods._getBoxSize();
                methods._getBoxPosition();
                methods._saveBg();
                methods._layout();
                currentScroll = methods.$scrollBox.scrollTop();
                methods.$scrollBox.scrollTop(_me.$ele.offset().top < _me.status.windowSize[1] < 0 ? 0 : _me.$ele.offset().top - _me.status.windowSize[1]);
                methods._getImgHeight();
                methods._movePosition();
                methods._displayImage();
                methods.$scrollBox.scrollTop(currentScroll);
            }
        },

        _initEvent: function (a) {
            var _me = this,
                $this = this.$ele;

            _me.event = $this.data('lzparallaxEvent');

            if (!_me.event) {
                _me.event = new parallaxEvent($this);
                $this.data('lzparallaxEvent', _me.event);
            }

            _me.event.setEvent()
        },

        _init: function (a) {
            var _me = this,
                $this = _me.$ele;
            _me.$ele.data('lzparallax-param', this._initParam(a));
            var param = _me.$ele.data('lzparallax-param');

            _me.methods = _me.$ele.data('lzparallaxMethods');

            if (!_me.methods) {
                _me.methods = new parallaxMethods($this);
                _me.$ele.data('lzparallaxMethods', _me.methods);
            }

            _me._initLayout();
            !param.stopEvent && (param.effect == 'fixed' || param.effect == 'scroll') && (_me._initEvent(), param.stopEvent = true);
        }
    }

    var tool = function (val) {
        var me = this;
        me.$ele = $(val);
        me.status = me.$ele.data('lzparallax-status');
        me.param = me.$ele.data('lzparallax-param');
        me.lzparallax = me.$ele.data('lzparallax');
        me.methods = me.$ele.data('lzparallaxMethods');
    }

    tool.prototype = {

        constructor: tool,

        refresh: function (status, flag) {
            var me = this,
                _me = me.lzparallax,
                $this = me.$ele,
                data = status,
                param = me.param;
            status = me.status;

            if (data == 'none') {
                $this.children('.lz-parallax-bg_clone').remove();
                $this.children('.lz-parallax-content,.lz-parallax-bg').remove();
                if (!flag) {
                    $this.css(status.bgVal);
                } else {
                    status.bgVal = {}
                }
                _me.$ele = me.methods.$ele = _me.$dom;
                return false;
            }

            me.$ele.css('backgroundImage') != 'none' && (me.status.bgVal = {});
            data != null && (me.param.effect = data);
            _me._initLayout();
            !param.stopEvent && (param.effect == 'fixed' || param.effect == 'scroll') && (_me._initEvent(), param.stopEvent = true);
        },

        imgTop: function () {
            _me.status.imgMin && _me._setDisplayImagePosition();
        }
    }

    $.fn.lzparallax = function (option, status, flag) {
        var sear = new RegExp(/IE|Edge/g),
            browser = $.isLzBrowser(),
            effect;
        var isIE = false;
        sear.test(browser[0]) && (isIE = true);

        var newParallax;

        if ($.isLzString(status)) {
            effect = status
        } else if ($.isLzObject(option)) {
            effect = option.effect
        } else {
            effect = ''
        }

        if ($.isLzObject(option) && $.isLzNumber(option.effect)) {
            option.effect = (option.effect > 0 && option.effect < 1) ? 'scroll' : option.effect == 0 ? 'fixed' : '';
        }
        if (!isIE && $.isPC()) {
            return $(this).each(function (index, val) {

                // 此判断为特殊需求
                if ($(val).closest('.w-fullpage-wrap').size() == 0) {

                    var newParallax = $(val).data('lzparallax');

                    if (!newParallax) {

                        $.isLzObject(option) && (status = option);

                        newParallax = new parallaxInit(val, status);

                        $(val).data('lzparallax', newParallax);

                    }

                    if ($.isLzObject(option)) {

                        newParallax._init(option);

                    } else if ($.isLzString(option)) {

                        var initTool = $(val).data('lzparallaxTool');

                        if (!initTool) {
                            initTool = new tool(val);
                            $(val).data('lzparallaxTool', initTool);
                        }

                        !!initTool[option] && initTool[option](status, flag);
                    }
                }

            });
        } else {
            return $(this)
        }
    }
})
