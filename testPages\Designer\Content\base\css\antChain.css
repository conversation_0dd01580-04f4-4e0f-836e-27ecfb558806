/* hover-effect */
.antChainBackgroundWrap {
    width: 100%;
    height:80px;
    background-color: #4c70ff;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-justify-content: center;
    -moz-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative;
}

.antChainBackgroundWrap .antChainBackground {
  width: 1920px;
  height: 80px;
  position: relative;
}

.antChainBackgroundWrap .antChainBackground .antChainBackgroundImg {
  width: 1920px;
  height: 80px;
}

.antChainBackgroundWrap .antChainBackground .antChainBtn {
  font-size: 24px;
  color: #ffffff;
  font-weight: bold;
  text-decoration: underline;
  position: absolute;
  left: 1105px;
  top: 27px;
  z-index: 2;
  cursor: pointer;
}

.antChainBackgroundWrap .antChainBackground .qrCodeWrap {
  position: absolute;
  top: -176px;
  left: 1105px;
  width: 220px;
  height: 256px;
  opacity: 0;
  z-index: -1;
  -webkit-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  transition: all 0.2s linear;
}

.antChainBackgroundWrap .antChainBackground .qrCodeWrap .qrCodeWrapImg {
  width: 220px;
  height: 256px;
}

.antChainBackgroundWrap .antChainBackground .qrCodeWrap .qrCodeImg {
  position: absolute;
  top: 25px;
  left: 25px;
  width: 170px;
  height: 170px;
}

.antChainBackgroundWrap .antChainBackground .qrCodeShow {
  top: 80px;
  opacity: 1;
  z-index: 999;
}

.antChainBackgroundWrap .antChainClose {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
  opacity: 0.6;
}

.antChainBackgroundWrap .antChainClose:hover {
    opacity: 1;
}

.antChainBackgroundWrap .antChainClose .antChainCloseImg {
  width: 26px;
  height: 26px;
}

.translate {
    -webkit-transform: translate(0px, 0px);
    -moz-transform: translate(0px, 0px);
    -ms-transform: translate(0px, 0px);
    -o-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
}