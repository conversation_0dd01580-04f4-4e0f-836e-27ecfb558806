; !function (factory) {
    // AMDģ�黯����
    if (typeof define === "function" && define.amd) {
        define(["jquery"], factory);
    } else {
        factory(jQuery);
    }
}(function ($) {

    'use strict';

    var lzpreview = {

        version: '1.3.7',

        author: 'lzx',

        date: '2017-11-23'

    }

    $.extend({
        isLzArray: function (v) {
            return Object.prototype.toString.call(v) === '[object Array]';
        },
        isLzObject: function (v) {
            return Object.prototype.toString.call(v) === '[object Object]';
        },
        isLzString: function (v) {
            return Object.prototype.toString.call(v) === '[object String]';
        },
        isLzBool: function (v) {
            return Object.prototype.toString.call(v) === '[object Boolean]';
        },
        isLzFunction: function (v) {
            return Object.prototype.toString.call(v) === '[object Function]';
        },
        isPC: function () {
            var userAgentInfo = navigator.userAgent,
                Agents = new Array("Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod"),
                flag = true;
            for (var v = 0; v < Agents.length; v++) {
                if (userAgentInfo.indexOf(Agents[v]) > 0) { flag = false; break; }
            }
            return flag;
        }
    });

    var previewEvent = function (val, init) {
        this.$ele = $(val);
        this.param = this.$ele.data('lzpreview-param');
        this.init = init;
    }

    previewEvent.prototype = {

        constructor: previewEvent,

        setEvent: function (plugin) {

            var event = this;
            this.plugin = plugin;

            $.each(this.param.event, function (index, val) {
                event[val] && event[val]();
            })
        },

        // reseize
        resize: function () {
            var me = this;
            var $this = this.$ele;
            var self = $this[0].previewMethods;
            var $dom = $("#lz-atlas-preview");
            $(window).resize(function () {
                self._calcSize();
                me.plugin.sliderToolB.refresh();
            });
        },

        // �رմ�ͼԤ��
        close: function () {
            var $dom = $("#lz-atlas-preview");
            var param = this.$ele.data('lzpreview-param');
            $dom.find('.lz-colseBut').on('click', function () {
                param.openZoom = false;
                $dom.fadeOut(150, function () {
                    $dom.remove();
                    $('.lz-preview-meta').remove();
                });
            });
        },

        // �Զ����ͷ
        corrow: function () {
            var $this = this.$ele;
            var self = $this[0].previewMethods;
            var $dom = $("#lz-atlas-preview");
            var $corrows = $dom.find('.lz-thumbnail-box .lz-but');
            var $corrow = $dom.find('.lz-atlas-box .lz-but');
            var plugin = this.plugin;
            var param = this.$ele.data('lzpreview-param');
            // ��ͼ�Զ�����Ƽ�ͷ
            $corrow.off('click').on('click', function () {
                var $this = $(this);
                var flag;
                if ($this.hasClass('lz-nextBut')) {
                    plugin.sliderToolB.prev();
                }
                if ($this.hasClass('lz-previousBut')) {
                    plugin.sliderToolB.next();
                }
                var initIndex = self._currentPointer();
                self._clSlider(initIndex);
                var index = initIndex - 1;
                param.openZoom = false;
                self._zoomBtn($(this), index);
                maxLen <= 10 && $corrows.eq(1).addClass('disabled');
                maxLen == 1 && $corrow.eq(1).addClass('disabled');
            });

            // ����ͼ�Զ�����Ƽ�ͷ
            $corrows.off('click').on('click', function () {
                var $this = $(this);
                if ($this.hasClass('lz-nextBut')) {
                    plugin.sliderToolS.prev();
                }
                if ($this.hasClass('lz-previousBut')) {
                    plugin.sliderToolS.next();
                }
                maxLen <= 10 && $corrows.eq(1).addClass('disabled');
                maxLen == 1 && $corrow.eq(1).addClass('disabled');
            });

            var maxLen = plugin.sliderToolB.$ele.find('li').length;

            // ͷβ��ͷ���ɵ��Ч��
            plugin.sliderToolB.on('switch', function (data) {
                if (plugin.sliderToolB.index() == 1) {
                    $corrow.eq(0).addClass('disabled');
                } else {
                    $corrow.eq(0).removeClass('disabled');
                }
                if (plugin.sliderToolB.index() == maxLen) {
                    $corrow.eq(1).addClass('disabled');
                } else {
                    $corrow.eq(1).removeClass('disabled');
                }
            });

            plugin.sliderToolS.on('switch', function () {
                if (plugin.sliderToolS.index() == 1) {
                    $corrows.eq(0).addClass('disabled');
                } else {
                    $corrows.eq(0).removeClass('disabled');
                }
                !plugin.sliderToolS.isLast() && $corrows.eq(1).removeClass('disabled');
            });
            plugin.sliderToolS.on('last', function () {
                $corrows.eq(1).addClass('disabled');
            });
        },
        pointer: function () {
            var $this = this.$ele;
            var self = $this[0].previewMethods;
            var $dom = $("#lz-atlas-preview");
            var plugin = this.plugin;
            var param = this.$ele.data('lzpreview-param');
            plugin.sliderToolS.$ele.find('li').on('click', function () {
                param.openZoom = false;
                self._currentChange($(this));
            });
        },

        // ����ͼƬ
        zoom: function () {
            var $this = this.$ele;
            var $dom = $("#lz-atlas-preview");
            var self = $this[0].previewMethods;
            var plugin = this.plugin;
            var param = this.$ele.data('lzpreview-param');
            $dom.find('.lz-see-change a').on('click', function () {
                param.openZoom = true;
                var index = plugin.sliderToolB.index() - 1;
                self._zoomBtn($(this), index);
            });
        },

        // ��������
        wheel: function () {
            var $this = this.$ele;
            var flag = false;
            var param = this.$ele.data('lzpreview-param');
            var $imgBox = $dom.find('.lz-atlas-box');
            var $img = $dom.find('.lz-atlas-box .lz-inner-img a');
            var index = -1;
            var self = $this[0].previewMethods;
            var plugin = this.plugin;

            $img.on('mouseenter', function () {
                index = $(this).closest('li').index();
                flag = true;
            }).on('mouseleave', function () {
                flag = false;
            });
            $imgBox.on('mousewheel.lzpreview DOMMouseScroll.lzpreview', function (e) {
                e = e || window.event;
                if (flag && param.openZoom) {
                    var $imgSizeAdd = $dom.find('.lz-see-change-lg');
                    var $imgSizeMinus = $dom.find('.lz-see-change-sm');
                    if (e.originalEvent.wheelDelta) {
                        e.originalEvent.wheelDelta < 0 ? self._zoomBtn($imgSizeAdd, index) : self._zoomBtn($imgSizeMinus, index);
                    } else if (e.originalEvent.detail) {
                        e.originalEvent.detail > 0 ? self._zoomBtn($imgSizeAdd, index) : self._zoomBtn($imgSizeMinus, index);
                    }
                } else if (flag && !param.openZoom) {
                    var initIndex;
                    if (e.originalEvent.wheelDelta) {
                        if (e.originalEvent.wheelDelta > 0) {
                            plugin.sliderToolB.prev();
                            initIndex = self._currentPointer();
                            self._clSlider(initIndex);
                        } else {
                            plugin.sliderToolB.next();
                            initIndex = self._currentPointer();
                            self._clSlider(initIndex)
                        };
                    } else if (e.originalEvent.detail) {
                        if (e.originalEvent.detail < 0) {
                            plugin.sliderToolB.prev();
                            initIndex = self._currentPointer();
                            self._clSlider(initIndex);
                        } else {
                            plugin.sliderToolB.next();
                            initIndex = self._currentPointer();
                            self._clSlider(initIndex)
                        }
                    }
                }
                return false
            });
        },

        // �ƶ�ͼƬ
        move: function () {
            var $this = this.$ele;
            var $dom = $("#lz-atlas-preview");
            var $img = $dom.find('.lz-atlas-box img');
            var param = this.$ele.data('lzpreview-param');
            var self = $this[0].previewMethods;
            var plugin = this.plugin;
            $img.on('mousedown', function (e) {
                if (param.moveFlag) {
                    param.canMove = true;
                    self._beginSite(e, $(this));
                } else {
                    param.canMove = false;
                }
            }).on('mousemove', function (e) {
                if (param.canMove) {
                    var indexs = plugin.sliderToolB.index() - 1;
                    var $currentImg = $img.eq(indexs);
                    self._moveSite(e);
                    self._endSite(e);
                    $currentImg.css({
                        left: self.endE[0] + self.imgLeft,
                        top: self.endE[1] + self.imgTop
                    });
                }
            });
            $img.closest('a').on('click', function () {
                if (param.moveFlag) {
                    return false;
                }
            });
            $('body').on('mouseup', function () {
                param.canMove = false;
            });
        }
    }

    // ����
    var previewPlugin = function (val, init) {
        this.$ele = $(val);
        this.param = this.$ele.data('lzpreview-param');
        this.init = init;
    }

    previewPlugin.prototype = {
        constructor: previewPlugin,
        _slider: function () {
            var $dom = $("#lz-atlas-preview");
            this.sliderToolB = $dom.find('.lz-atlas-box .lz-tempWrap').lzslider({
                cycle: false,//ѭ������
                auto: false,//�Զ�����
                arrowStyle: false,//Ĭ�ϼ�ͷ��ʽ
                event: ['touch'],//Ĭ���¼�
                speed: 0,
                num: 1
            });

            this.sliderToolS = $dom.find('.lz-thumbnail-box .lz-tempWrap').lzslider({
                cycle: false,//ѭ������
                auto: false,//�Զ�����
                arrowStyle: false,//Ĭ�ϼ�ͷ��ʽ
                event: ['touch'],//Ĭ���¼�
                speed: 200,
                num: 1
            });
        }
    }

    // ������
    var previewMethods = function (val) {
        this.$ele = $(val);
        this.param = this.$ele.data('lzpreview-param');
        this.flag = true;
    }

    previewMethods.prototype = {
        constructor: previewMethods,
        // ����ͼƬ����
        _setImgLink: function () {
            var $dom = self.$dom = $('#lz-atlas-preview');
            var $bigImg = $dom.find('.lz-atlas-box .lz-inner-img-box li');
            var me = this;
            $bigImg.each(function (index, el) {
                $(el).children('a').attr({
                    'href': me.param.imgLink[index],
                    'target': me.param.imgTarget[index]
                });
            });
        },
        // ������ʼ����ͼԤ��
        _calcSize: function () {
            var $dom = self.$dom = $('#lz-atlas-preview');
            var $bigImg = $dom.find('.lz-atlas-box .lz-inner-img-box li');
            var me = this;
            $dom.show();
            var windowWidth = $(window).width();
            var itemHeight = $bigImg.parent().height();
            $bigImg.css({
                width: windowWidth,
                height: itemHeight + 'px'
            });
            $dom.find('.lz-atlas-box .lz-but').css({
                height: itemHeight,
                lineHeight: itemHeight + 'px',
                top: '34px'
            });
            !me.flag && $dom.hide();
            me.flag = true;
            $dom.find('.lz-img-bd').css('visibility', 'visible');
        },

        _clickList: function ($this, plugin) {
            var $dom = self.$dom = $('#lz-atlas-preview'),
                indexs = 0,
                _me = this,
                $corrows = $dom.find('.lz-thumbnail-box .lz-but'),
                $corrow = $dom.find('.lz-atlas-box .lz-but'),
                pageNum = _me.$ele.find('.xn-pager .w-page-cm.active').attr('jp-data');
            if (!pageNum || pageNum === undefined) {
                $.isLzFunction(_me.param.pageNum) && _me.param.pageNum()
            }
            //$this.index()�ڷ�ҳ�� UL�»�����һ��div ���ʸ�����ȡ�����ķ�ʽ ��Ϊprevall
            indexs = _me.param.clickSelect == '' ? $this.prevAll('li').length : $this.closest(_me.param.itemSelect).index();
            (isNaN(pageNum) || !pageNum) && (pageNum = 1);
            var listIndex = _me.param.pageSize * (pageNum - 1) + indexs + 1;
            $dom = $("#lz-atlas-preview");
            var maxLen = plugin.sliderToolB.$ele.find('li').length;

            this.plugin = plugin;
            plugin.sliderToolB.index(listIndex);
            var currentIndex = this._currentPointer();
            this._clSlider(currentIndex);
            $dom.fadeIn(150);
            this._zoomBtn($(this), indexs);

            if (plugin.sliderToolB.index() == 1) {
                $corrow.eq(0).addClass('disabled');
            }
            if (plugin.sliderToolS.index() == 1) {
                $corrows.eq(0).addClass('disabled');
            }
            plugin.sliderToolB.isLast() && $corrow.eq(1).addClass('disabled');
            plugin.sliderToolS.isLast() && $corrows.eq(1).addClass('disabled');
            maxLen <= 10 && $corrows.eq(1).addClass('disabled');
            maxLen == 1 && $corrow.eq(1).addClass('disabled');
        },

        // �л�����
        _currentPointer: function () {
            var initIndex = this.plugin.sliderToolB.index();
            this.plugin.sliderToolS.$ele.find('li').removeClass('lz-action').eq(initIndex - 1).addClass('lz-action');
            return initIndex
        },

        _currentChange: function ($this) {
            var indexs = $this.index();
            this.plugin.sliderToolS.$ele.find('li').removeClass('lz-action').eq(indexs).addClass('lz-action');
            this.plugin.sliderToolB.index(indexs + 1);
            this._zoomBtn($(this), indexs);
        },

        // ����ͼ�������
        _clSlider: function (initIndex) {
            var indexs = this.plugin.sliderToolS.index();
            if (initIndex <= indexs + 2) {
                this.plugin.sliderToolS.index(initIndex - 3);//ǰ����
            }
            if (initIndex >= indexs + 7) {
                this.plugin.sliderToolS.index(initIndex - 6);//������
            }
        },

        _beginSite: function (event, $currentImg) {
            var me = this;
            var event = window.event || event;
            event.preventDefault ? event.preventDefault() : event.returnValue = false;
            var scrollX = $(window).scrollLeft(),
                scrollY = $(window).scrollTop();
            var x = event.pageX || event.clientX + scrollX,
                y = event.pageY || event.clientY + scrollY;
            me.beginE = [x, y];
            me.imgTop = $currentImg.position().top;
            me.imgLeft = $currentImg.position().left;
        },
        // ��ȡ�ƶ�ʱ��λ��
        _moveSite: function (event) {
            var event = window.event || event;
            event.preventDefault ? event.preventDefault() : event.returnValue = false;
            var scrollX = $(window).scrollLeft(),
                scrollY = $(window).scrollTop();
            var x = event.pageX || event.clientX + scrollX,
                y = event.pageY || event.clientY + scrollY;
            this.moveE = [x, y];
        },
        // ��ȡ�ƶ����λ��ֵ
        _endSite: function () {
            var me = this;
            me.endE = [me.moveE[0] - me.beginE[0], me.moveE[1] - me.beginE[1]];
        },

        // ����Ŵ���С��ť
        _zoomBtn: function ($this, index) {
            var param = this.$ele.data('lzpreview-param');
            var $dom = $("#lz-atlas-preview");
            var $numDom = $dom.find('.lz-see-change-txt');
            var $img = $dom.find('.lz-atlas-box img');
            var num = +$numDom.text().replace('%', '');
            var max = 500,//���Ŵ����
                min = 5;//��С��С����
            var mleft = 0,
                mtop = 0,
                imgWid = 0,
                imgHei = 0;
            var cssObj = {
                position: 'absolute',
                maxWidth: 'none',
                maxHeight: 'none',
                left: '50%',
                top: '50%',
                zIndex: 300
            }
            var resetCssObj = {
                position: '',
                width: '',
                height: '',
                maxWidth: '',
                maxHeight: '',
                left: '',
                top: '',
                zIndex: '',
                marginLeft: '',
                marginTop: ''
            }
            var $currentImg = $img.eq(index);
            $this.hasClass('lz-see-change-sm') ? num -= 5 : $this.hasClass('lz-see-change-lg') ? num += 5 : num = 100;
            !param.getImgFlag && (this.initWidth = $currentImg.width());
            if (num >= min && num <= max) {
                $numDom.text(num + '%');
                if (num == 100) {
                    param.getImgFlag = false;
                    param.moveFlag = false;
                    $img.closest('a').css('cursor', 'pointer');
                    $currentImg.css(resetCssObj);
                } else {
                    imgWid = ~~(this.initWidth * (num / 100));
                    imgHei = ~~$currentImg.height();
                    $currentImg.css('width', imgWid);
                    $currentImg.css(cssObj);
                    mleft = -imgWid / 2;
                    mtop = -imgHei / 2;
                    $currentImg.css({
                        marginLeft: mleft,
                        marginTop: mtop
                    });
                    param.getImgFlag = true;
                    if (num > 100) {
                        param.moveFlag = true;
                        $img.closest('a').css('cursor', '')
                    } else {
                        param.moveFlag = false;
                        $img.closest('a').css('cursor', 'pointer')
                    }

                }
            }
        },

        // ����С�ڿɹ������ʱ����
        _centerThumbnail: function () {
            var $thumbnail = $('#lz-atlas-preview .lz-thumbnail-box .lz-tempWrap .lz-inner-img-box');
            var thumbnailWidth = $thumbnail.width();
            if (thumbnailWidth < 670) {
                $thumbnail.parent().css('width', thumbnailWidth + 'px');
            }
        },

        // ��ʾ�����ؼ�ͷ
        _showSwitch: function () {
            var param = this.param;

            if (!param.arrow) {
                $('#lz-atlas-preview').find('.lz-atlas-box .lz-img-but').hide();
                $('#lz-atlas-preview').find('.lz-atlas-box .lz-img-bd .lz-tempWrap ul:first-child li a').css('maxWidth', '90%')
            } else {
                $('#lz-atlas-preview').find('.lz-atlas-box .lz-img-but').show();
                $('#lz-atlas-preview').find('.lz-atlas-box .lz-img-bd .lz-tempWrap ul:first-child li a').css('maxWidth', '')
            }
        },

        // ��ʾ����������ͼ
        _showThumbnail: function () {
            var param = this.param;
            if (!param.thumbnail || !$.isPC()) {
                $('#lz-atlas-preview').find('.lz-thumbnail-box').hide();
                $('#lz-atlas-preview').find('.lz-atlas-box .lz-img-bd .lz-tempWrap ul:first-child').css('bottom', '34px');
                $('#lz-atlas-preview').find('.lz-see-change').css('bottom', '38px')

            } else {
                $('#lz-atlas-preview').find('.lz-thumbnail-box').show();
                $('#lz-atlas-preview').find('.lz-atlas-box .lz-img-bd .lz-tempWrap ul:first-child').css('bottom', '')
                $('#lz-atlas-preview').find('.lz-see-change').css('bottom', '')
            }
        }
    }

    var previewInit = function (val, a, b, c) {
        var me = this;
        me.$ele = $(val);
    };


    previewInit.prototype = {

        constructor: previewInit,

        defaults: {
            itemSelect: '.w-atlas-ul li',
            clickSelect: '',
            imgUrl: [],
            cssLink: 'control/css/atlas-preview.css',
            imgAlt: [],
            imgLink: [],
            imgTarget: [],
            pageSize: 1,
            length: 0,
            moveFlag: false,
            initWidth: 0,
            getImgFlag: false,
            canMove: false,
            openZoom: false,
            arrow: true,
            thumbnail: true,
            event: ['resize', 'close', 'corrow', 'pointer', 'zoom', 'move', 'wheel']
        },

        // ��ʼ������
        _initParam: function (a) {
            var param = this.defaults;
            if ($.isLzObject(a)) {
                param = $.extend(true, {}, param, a);
            }
            param.imgUrl = $.isLzArray(param.imgUrl) ? param.imgUrl : ($.isLzString(param.imgUrl) && param.imgUrl != '') ? param.imgUrl.split(' ') : [param.imgUrl];
            param.tag = $.isLzArray(param.imgAlt) ? param.imgAlt : ($.isLzString(param.imgAlt) && param.imgAlt != '') + [param.imgAlt];
            param.length = param.imgUrl.length;
            return param;
        },

        //��ʼ������
        _layout: function () {
            var me = this,
                $this = me.$ele,
                lzpreviewDom = '<div class="lz-atlas-preview" id="lz-atlas-preview">' +
                    '<a href="javascript:void(0);" class="lz-colseBut">' +
                    '<i class="lz-itemicon mw-iconfont">&#xa158;</i>' +
                    '</a>' +
                    '<div class="lz-display">' +
                    '<div class="lz-atlas-box">' +
                    '<div class="lz-see-change">' +
                    '<a href="javascript:;" class="lz-see-change-sm mw-iconfont">&#xa157;</a>' +
                    '<a href="javascript:;" class="lz-see-change-lg mw-iconfont">&#xa156;</a>' +
                    '<span class="lz-see-change-txt">100%</span>' +
                    '</div>' +
                    '</div>' +
                    '<div class="lz-thumbnail-box"></div>' +
                    '</div>' +
                    '</div>',
                lzpreviewInnerDom = '<div class="lz-img-bd">' +
                    '<div class="lz-img-but">' +
                    '<a href="javascript:void(0)" class="lz-nextBut lz-but"><i class="lz-itemicon mw-iconfont">&#xa134;</i></a>' +
                    '<a href="javascript:void(0)" class="lz-previousBut lz-but"><i class="lz-itemicon mw-iconfont">&#xa132;</i></a>' +
                    '</div>' +
                    '<div class="lz-tempWrap">' +
                    '<ul class="lz-inner-img-box">' +
                    '<li class="lz-inner-img">' +
                    '<a href="javascript:void(0)" target="_self">' +
                    '<div class="lz-reference"></div>' +
                    '<img class="lazyload lz-preview-img" id="imgDraggable" src="" alt="">' +
                    '</a>' +
                    '</li>' +
                    '</ul>' +
                    '</div>' +
                    '</div>',
                param = $this.data('lzpreview-param');

            $('#lz-atlas-preview').remove();
            $('body').append(lzpreviewDom);

            var $dom = $('#lz-atlas-preview');
            $dom.find('.lz-atlas-box').append(lzpreviewInnerDom);
            $dom.find('.lz-thumbnail-box').append(lzpreviewInnerDom);

            var fragmeng = document.createDocumentFragment(),
                imgLi = $dom.find('.lz-img-bd .lz-inner-img-box').html();
            for (var i = 0; i < param.length; i++) {
                fragmeng.appendChild($(imgLi)[0]);
                $(fragmeng.childNodes[i]).find('.lz-preview-img').attr({
                    src: param.imgUrl[i],
                    alt: param.imgAlt[i]
                })
            }
            $dom.find('.lz-inner-img-box').html(fragmeng);
        },
        //�ƶ�����ק�¼�
        _initMobileEvent: function () {
            var touchScale = function (seletor) {
                var $el = $(seletor)[0]
                $el.addEventListener('touchstart', function (e) {
                    //Ĭ����ֹ�����¼���ð��
                    e.preventDefault();
                    e.cancelBubble = true;
                    if (e.targetTouches.length == 1) {
                        //�����
                        var disX = e.targetTouches[0].pageX - $el.offsetLeft;
                        var disY = e.targetTouches[0].pageY - $el.offsetTop;

                        function fnMove(e) {
                            var left = e.targetTouches[0].pageX - disX;
                            var top = e.targetTouches[0].pageY - disY;
                            if (left < -$(seletor).width() + 50) {
                                left = -$(seletor).width() + 50
                            } else if (left > $(seletor).parent().width() - 50) {
                                left = $(seletor).parent().width() - 50
                            }
                            if (top < -$(seletor).height() + 50) {
                                top = -$(seletor).height() + 50
                            } else if (top > $(seletor).parent().height() - 50) {
                                top = $(seletor).parent().height() - 50
                            }
                            $el.style.left = left + 'px';
                            $el.style.top = top + 'px';
                        }
                        function fnEnd() {
                            $el.removeEventListener('touchmove', fnMove, false);
                            $el.removeEventListener('touchend', fnEnd, false);
                        }
                        $el.addEventListener('touchmove', fnMove, false);
                        $el.addEventListener('touchend', fnEnd, false);
                    } else if (e.targetTouches.length >= 2) {
                        var w = $el.offsetWidth, h = $el.offsetHeight, scale = w/h;
                        var dis = Math.sqrt(Math.pow(e.targetTouches[0].clientX - e.targetTouches[1].clientX, 2) + Math.pow(e.targetTouches[0].clientY - e.targetTouches[1].clientY, 2));
                        if (e.targetTouches.length >= 2) {
                            $el.addEventListener('touchmove', fnMove, false);
                            $el.addEventListener('touchend', fnEnd, false);
                        }
                        function fnMove(e) {
                            e.preventDefault();
                            e.cancelBubble = true;
                            if (e.targetTouches.length >= 2) {
                                $(seletor).css({ 'max-width': 'none', 'max-height': 'none' })
                                var dis2 = Math.sqrt(Math.pow(e.targetTouches[0].clientX - e.targetTouches[1].clientX, 2) + Math.pow(e.targetTouches[0].clientY - e.targetTouches[1].clientY, 2));
                                var newW = dis2 * w / dis;
                                if ($(seletor).position().left + newW < 50) {
                                    newW = 50 - $(seletor).position().left
                                }
                                if ($(seletor).position().top + newW / scale < 50) {
                                    newW = scale * (50 - $(seletor).position().top)
                                }
                                if (newW > w * 3) {
                                    newW = w * 3;
                                }
                                $el.style.width = newW + 'px';
                                $el.style.lineHeight = $el.style.height = newW / scale + 'px';
                            }
                        }
                        function fnEnd() {
                            $el.removeEventListener('touchmove', fnMove, false);
                            $el.removeEventListener('touchend', fnEnd, false);
                        }
                    }
                    
                }, false)
            }
            if (location.href.indexOf("PrevNewsContentPage") != -1 || location.href.indexOf("newsinfo") != -1) {
                console.log("imgMove")
                $("#imgDraggable").load(function () {
                    $(".lz-see-change").hide();
                    $(".lz-atlas-box .lz-inner-img-box").addClass("lz-inner-img-box-size")
                    $(".lz-atlas-box .lz-inner-img").addClass("lz-inner-img-size")
                    $("#imgDraggable").css({
                        'position': 'absolute',
                        'left': ($(window).width() * 0.9 - $("#imgDraggable").width()) / 2,
                        'top': ($(window).height() - 68 - $("#imgDraggable").height()) / 2,
                    });
                    touchScale('#imgDraggable')
                })
            }
        },

        // ��ʼ���¼�
        _initEvent: function (plugin) {
            var $this = this.$ele,
                event = new previewEvent($this, this);
            event.setEvent(plugin);
        },

        // ��ʼ������
        _initPlugin: function () {
            var plugin = new previewPlugin(this);
            plugin._slider();
            return plugin;
        },

        // ��ʼ����ʽ��
        _initLoadCss: function () {
            var link = '<link id="lz-preview-css" href="' + this.param.cssLink + '" rel="stylesheet">';
            $('#lz-preview-css').size() == 0 && $('head').append(link);
        },

        _initLoadMeta: function () {
            // var mate = '<meta class="lz-preview-meta" content="yes" name="apple-mobile-web-app-capable">'+
            // 			'<meta class="lz-preview-meta" content="black" name="apple-mobile-web-app-status-bar-style">'+
            // 			'<meta class="lz-preview-meta" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">'
            // 	$('head').prepend(mate);
        },

        _init: function (a) {
            var me = this,
                self = this.$ele.get(0),
                $this = me.$ele;
            me.$ele.data('lzpreview-param', this._initParam(a));
            me.param = this.$ele.data('lzpreview-param');
            self.previewMethods = new previewMethods(self);
            self = $this[0].previewMethods;
            var $listClick = '';
            me._initLoadCss();
            if ($.isLzObject(me.param.itemSelect)) {
                $listClick = me.param.itemSelect
            } else {
                $listClick = me.param.clickSelect == '' ? $this.find(me.param.itemSelect) : $this.find(me.param.clickSelect);
            }
            $listClick.off('click.lzpreview').on('click.lzpreview', function () {
                me._initLoadMeta();
                var $this = $(this);
                me._layout();
                self._showSwitch();
                self._showThumbnail();
                self._setImgLink();
                self._calcSize();
                var plugin = me._initPlugin();
                self._clickList($this, plugin);
                self._centerThumbnail();
                me._initEvent(plugin);
                if (!$.isPC()) {
                    me._initMobileEvent();
                }
            });
        }
    }

    var tool = function (val) {
        var me = this;
        me.$ele = $(val);
        me.param = this.$ele.data('lzpreview-param');

        me.previewMethods = this.$ele[0].previewMethods;
        me.flag = true;
    };

    tool.prototype = {
        constructor: previewInit,

        seturl: function (url) {
            this.param.imgUrl = $.isLzArray(url) ? url : ($.isLzString(url) && url != '') ? url.split() : '';
        },

        arrowSwitch: function (option) {

            $.isLzBool(option) && (this.previewMethods.param.arrow = option);
        },

        thumbnailSwitch: function (option) {
            $.isLzBool(option) && (this.previewMethods.param.thumbnail = option);
        }
    }

    // ������
    $.fn.lzpreview = function (option, status) {
        return $.each(this, function (index, val) {
            if (!$(val).data('lzpreview-stamp')) {

                var newPreview = $(val).data('lzpreview');

                if (!newPreview) {

                    $.isLzObject(option) && (status = option);

                    newPreview = new previewInit(val, option, status)

                    $(val).data('lzparallax', newPreview);

                }

                if ($.isLzObject(option)) {

                    newPreview._init(option);

                } else if ($.isLzString(option)) {

                    var initTool = $(val).data('lzparallaxTool');

                    if (!initTool) {
                        initTool = new tool(val);
                        $(val).data('lzparallax', initTool);
                    }

                    !!initTool[option] && initTool[option](status);
                }
            }
        });
    }
});
