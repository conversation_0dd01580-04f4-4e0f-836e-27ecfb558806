<html>
  <head>  
    <meta name="viewport" content="width=device-width">
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
      <meta name="description">
      <meta name="renderer" content="webkit">
      <meta name="applicable-device" content="pc">
      <meta http-equiv="Cache-Control" content="no-transform">
      <link href="testPages/Designer/Content/bottom/pcstyle.css?_version=20230608105041" rel="stylesheet" type="text/css">
      <link href="testPages/Content/public/css/reset.css?_version=20230608105041" rel="stylesheet" type="text/css">
      <link href="testPages/Designer/Content/base/css/pager.css?_version=20230608105041" rel="stylesheet" type="text/css">
      <link href="testPages/Designer/Content/base/css/hover-effects.css?_version=20230608105041" rel="stylesheet" type="text/css">
      <link href="testPages/Designer/Content/base/css/antChain.css?_version=20230608105041" rel="stylesheet" type="text/css">
      <link href="testPages/pubsf/10197/10197361/css/60150_Pc_zh-CN.css?preventCdnCacheSeed=4381bb0dcb0c419db6a82476bc0db30b" rel="stylesheet">
      <link href="testPages/static/iconfont/font_somdr6xou4/iconfont.css" rel="stylesheet" />
      <script src="testPages/Scripts/JQuery/jquery-3.6.3.min.js?_version=20230608105042" type="text/javascript"></script>
      <script src="testPages/Designer/Scripts/jquery.lazyload.min.js?_version=20230608105042" type="text/javascript"></script>
      <script src="testPages/Designer/Scripts/smart.animation.min.js?_version=20230608105042" type="text/javascript"></script>
      <script src="testPages/Designer/Content/Designer-panel/js/kino.razor.min.js?_version=20230608105041" type="text/javascript"></script>
      <script src="testPages/Scripts/common.min.js?v=20200318&amp;_version=20230608105042" type="text/javascript"></script>
      <script src="testPages/Administration/Scripts/admin.validator.min.js?_version=20230608105035" type="text/javascript"></script>
      <script src="testPages/Administration/Content/plugins/cookie/jquery.cookie.js?_version=20230608105034" type="text/javascript"></script>
      <script type="text/javascript" id="jssor-all" src="testPages/Designer/Scripts/jssor.slider-22.2.16-all.min.js?_version=20230608105042"></script>
      <script type="text/javascript" id="slideshown" src="testPages/Designer/Scripts/slideshow.js?_version=20230608105042"></script>
      <script type="text/javascript" id="lzparallax" src="testPages/static/lzparallax/1.0.0/lz-parallax.min.js"></script>
      <script type="text/javascript" id="SuperSlide" src="testPages/Designer/Content/Designer-panel/js/jquery.SuperSlide.2.1.1.js"></script>
      <script type="text/javascript" id="jqPaginator" src="testPages/Scripts/statics/js/jqPaginator.min.js"></script>
      <script type="text/javascript" id="lz-slider" src="testPages/Scripts/statics/js/lz-slider.min.js"></script>
      <script type="text/javascript" id="lz-preview" src="testPages/Scripts/statics/js/lz-preview.min.js"></script>
    <title>徐州联创自动化科技有限公司</title><link id="lz-preview-css" href="testPages/Content/css/atlas-preview.css" rel="stylesheet">
    </head>
    <body id="smart-body" area="main">
      <script type="text/javascript">
          $(function() {
          if ("False"=="True") {
          $('#mainContentWrapper').addClass('translate');
          $('#antChainWrap').fadeIn(500);
  
          $('#closeAntChain').off('click').on('click', function(){
          $('#antChainWrap').fadeOut('slow',function(){
          $('#mainContentWrapper').removeClass('translate');
          });
          $(document).off("scroll",isWatchScroll);
  
          });
          $('#showQrcodeBtn').off('click').on('click', function(){
          $('#qrCodeWrappper').toggleClass('qrCodeShow');
          });
          $(document).scroll(isWatchScroll)
          }
  
  
          function isWatchScroll(){
          var scroH = $(document).scrollTop();
          if(scroH >= 80) {
          $('#mainContentWrapper').removeClass('translate');
          } else {
          $('#mainContentWrapper').addClass('translate');
          }
          }
  
  
          })
      </script>
      <div id="mainContentWrapper" style="background-color: transparent; background-image: none; background-repeat: no-repeat;background-position:0 0; background:-moz-linear-gradient(top, none, none);background:-webkit-gradient(linear, left top, left bottom, from(none), to(none));background:-o-linear-gradient(top, none, none);background:-ms-linear-gradient(top, none, none);background:linear-gradient(top, none, none);;
       position: relative; width: 100%;min-width:1200px;background-size: auto;" bgscroll="none">
      <div style="background-color: transparent; background-image: none; background-repeat: no-repeat;background-position:0 0; background:-moz-linear-gradient(top, none, none);background:-webkit-gradient(linear, left top, left bottom, from(none), to(none));background:-o-linear-gradient(top, none, none);background:-ms-linear-gradient(top, none, none);background:linear-gradient(top, none, none);;
           position: relative; width: 100%;min-width:1200px;background-size: auto;" bgscroll="none">
          <div class=" header" cpid="60135" id="smv_Area0" style="width: 1200px; height: 70px;  position: relative; margin: 0 auto">
              <div id="smv_tem_1_45" ctype="banner" class="esmartMargin smartAbs smartFixed   " cpid="60135" cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="True" pvid="" tareaid="Area0" re-direction="y" daxis="Y" isdeletable="True" style="height: 70px; width: 100%; left: 0px; top: 0px;right:0px;margin:auto;z-index:13;"><div class="yibuFrameContent tem_1_45  banner_Style1  " style="overflow:visible;;"><div class="fullcolumn-inner smAreaC" id="smc_Area0" cid="tem_1_45" style="width:1200px">
      </div>
  <div id="bannerWrap_tem_1_45" class="fullcolumn-outer" style="position: absolute; top: 0px; bottom: 0px; left: 0px; width: 1784px;">
  </div>
  
  <script type="text/javascript">
  
      $(function () {
          var resize = function () {
              $("#smv_tem_1_45 >.yibuFrameContent>.fullcolumn-inner").width($("#smv_tem_1_45").parent().width());
              $('#bannerWrap_tem_1_45').fullScreen(function (t) {
                  if (VisitFromMobile()) {
                      t.css("min-width", t.parent().width())
                  }
              });
          }
          if (typeof (LayoutConverter) !== "undefined") {
              LayoutConverter.CtrlJsVariableList.push({
                  CtrlId: "tem_1_45",
                  ResizeFunc: resize,
              });
          } else {
              $(window).resize(function (e) {
                  if (e.target == this) {
                      resize();
                  }
              });
          }
  
          resize();
      });
  </script>
  </div></div><div id="smv_tem_2_50" ctype="image" class="esmartMargin smartAbs smartFixed  " cpid="60135" cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="" tareaid="Area0" re-direction="all" daxis="All" isdeletable="True" style="height: 50px; width: 187px; left: 50px; top: 10px;z-index:16;"><div class="yibuFrameContent tem_2_50  image_Style1  " style="overflow:visible;;">
      <div class="w-image-box image-clip-wrap" data-filltype="0" id="div_tem_2_50" style="height: 50px; width: 187px;">
          <a target="_self" href="index.html">
              <img loading="lazy" src="images/LOGO.ico" alt="未标题-1" title="" id="img_smv_tem_2_50" 
              style="width: 50px; height: 50px;" class="">
              <label>联创智能</label>
          </a>
      </div>
  
      <script type="text/javascript">
          $(function () {
              
                  InitImageSmv("tem_2_50", "185", "50", "0");
              
                   });
      </script>
  
  </div></div><div id="smv_tem_28_38" ctype="nav" class="esmartMargin smartAbs smartFixed   " cpid="60135" cstyle="Style7" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="" tareaid="Area0" re-direction="all" daxis="All" isdeletable="True" style="height: 70px; width: 700px; right: 85px; top: 0px;z-index:18;"><div class="yibuFrameContent tem_28_38  nav_Style7  " style="overflow:visible;;"><div id="nav_tem_28_38" class="nav_pc_t_7">
      <ul class="w-nav" navstyle="style7" style="width:auto;">
                  <li class="w-nav-inner" style="height:70px;line-height:70px;">
                      <div class="w-nav-item current">
                          <a href="index.html" target="_self" class="w-nav-item-link">
                              <span class="mw-iconfont"></span>
                              <span class="w-link-txt">首页</span>
                          </a>
                          
                      </div>
                  </li>
                  <li class="w-nav-inner" style="height:70px;line-height:70px;">
                      <div class="w-nav-item">
                          <a href="smartAI.html" target="_self" class="w-nav-item-link">
                              <span class="mw-iconfont"></span>
                              <span class="w-link-txt">AI平台软件</span>
                          </a>
                          
                      </div>
                          <ul class="w-subnav" style="width: 220px; display: none; height: 400px; padding-top: 0px; margin-top: 0px; padding-bottom: 0px; margin-bottom: 0px;">
                                  <li class="w-subnav-item">
                                      <a href="smartAI_CM.html" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                          <span class="mw-iconfont"></span>
                                          <span class="w-link-txt">采煤工作面智能监察系统</span>
                                      </a>
                                     
                                  </li>
                                  <li class="w-subnav-item">
                                      <a href="smartAI_JJ.html" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                          <span class="mw-iconfont"></span>
                                          <span class="w-link-txt">掘进工作面智能监察系统</span>
                                      </a>
                                     
                                  </li>
                                  <li class="w-subnav-item">
                                      <a href="smartAI_CNL.html" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                          <span class="mw-iconfont"></span>
                                          <span class="w-link-txt">超能力生产智能分析系统</span>
                                      </a>
                                     
                                  </li>
                                  <li class="w-subnav-item">
                                      <a href="smartAI_CDY.html" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                          <span class="mw-iconfont"></span>
                                          <span class="w-link-txt">超定员智能分析系统</span>
                                      </a>
                                     
                                  </li>
                                  <li class="w-subnav-item">
                                      <a href="smartAI_JXGJDD.html" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                          <span class="mw-iconfont"></span>
                                          <span class="w-link-txt">井下关键地点视频智能分析系统</span>
                                      </a>
                                     
                                  </li>
                                  <li class="w-subnav-item">
                                      <a href="smartAI_DDSKG.html" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                          <span class="mw-iconfont"></span>
                                          <span class="w-link-txt">调度室空岗、睡岗智能分析系统</span>
                                      </a>
                                  </li>
                                  <li class="w-subnav-item">
                                    <a href="smartAI_YJZH.html" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                        <span class="mw-iconfont"></span>
                                        <span class="w-link-txt">应急指挥通讯系统</span>
                                    </a>
                                </li>
                                <li class="w-subnav-item">
                                    <a href="smartAI_MKXJRY.html" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                        <span class="mw-iconfont"></span>
                                        <span class="w-link-txt">煤矿下井人员、设备精准定位系统</span>
                                    </a>
                                </li>
                                <li class="w-subnav-item">
                                    <a href="smartAI_FZYS.html" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                        <span class="mw-iconfont"></span>
                                        <span class="w-link-txt">辅助运输智能检测系统</span>
                                    </a>
                                </li>
                                <li class="w-subnav-item">
                                    <a href="smartAI_JSYTSF.html" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                        <span class="mw-iconfont"></span>
                                        <span class="w-link-txt">一通三防智能检测系统</span>
                                    </a>
                                </li>


                          </ul>
                  </li>
                  <li class="w-nav-inner" style="height:70px;line-height:70px;">
                      <div class="w-nav-item">
                          <a href="kyList.html" target="_self" class="w-nav-item-link">
                              <span class="mw-iconfont"></span>
                              <span class="w-link-txt">智能硬件</span>
                          </a>
                        </div>
                  </li>
                  <li class="w-nav-inner" style="height:70px;line-height:70px;">
                      <div class="w-nav-item">
                          <a href="sovleWay.html" target="_self" class="w-nav-item-link">
                              <span class="mw-iconfont"></span>
                              <span class="w-link-txt">系统解决方案</span>
                          </a>
                      </div>
                          <ul class="w-subnav" style="width: 190px; display: none; height: 280px; padding-top: 0px; margin-top: 0px; padding-bottom: 0px; margin-bottom: 0px;">
                                  <li class="w-subnav-item">
                                      <a href="sovleWay.html#smv_con_181_4" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                          <span class="mw-iconfont"></span>
                                          <span class="w-link-txt">综合自动化管理平台</span>
                                      </a>
                                  </li>
                                  <li class="w-subnav-item">
                                      <a href="sovleWay.html#smv_con_182_20" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                          <span class="mw-iconfont"></span>
                                          <span class="w-link-txt">变电所自动化系统</span>
                                      </a>
                                     
                                  </li>
                                  <li class="w-subnav-item">
                                      <a href="sovleWay.html#smv_con_183_34" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                          <span class="mw-iconfont"></span>
                                          <span class="w-link-txt">运输自动化系统</span>
                                      </a>
                                     
                                  </li>
                                  <li class="w-subnav-item">
                                      <a href="sovleWay.html#smv_con_184_52" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                          <span class="mw-iconfont"></span>
                                          <span class="w-link-txt">排水自动化系统</span>
                                      </a>
                                     
                                  </li>
                                  <li class="w-subnav-item">
                                      <a href="sovleWay.html#smv_con_185_5" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                          <span class="mw-iconfont"></span>
                                          <span class="w-link-txt">通风机自动化系统</span>
                                      </a>
                                     
                                  </li>
                                  <li class="w-subnav-item">
                                      <a href="sovleWay.html#smv_con_186_19" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                          <span class="mw-iconfont"></span>
                                          <span class="w-link-txt">压风自动化系统</span>
                                      </a>
                                  </li>
                                  <li class="w-subnav-item">
                                    <a href="sovleWay.html#smv_con_172_19" target="_self" class="w-subnav-link" style="height:40px;line-height:40px">
                                        <span class="mw-iconfont"></span>
                                        <span class="w-link-txt">万兆环网集控系统</span>
                                    </a>
                                </li>
                          </ul>
                  </li>
                  <li class="w-nav-inner" style="height:70px;line-height:70px;">
                      <div class="w-nav-item">
                          <a href="supportService.html" target="_self" class="w-nav-item-link">
                              <span class="mw-iconfont"></span>
                              <span class="w-link-txt">支持服务</span>
                          </a>
                      </div>
                  </li>
                  <li class="w-nav-inner" style="height:70px;line-height:70px;">
                      <div class="w-nav-item">
                          <a href="aboutUs.html" target="_self" class="w-nav-item-link">
                              <span class="mw-iconfont"></span>
                              <span class="w-link-txt">关于我们</span>
                          </a>
                          
                      </div>
                  </li>
  
      </ul>
  </div>
  <script>
      $(function () {
          $('#nav_tem_28_38 .w-nav').find('.w-subnav').hide();
          var $this, item, itemAll;
  
          if ("False".toLocaleLowerCase() == "true") {
          } else {
              //$("#nav_tem_28_38 .w-subnav").css("width", "190" + "px");
          }
          
          $('#nav_tem_28_38 .w-nav').off('mouseenter').on('mouseenter', '.w-nav-inner', function () {
              itemAll = $('#nav_tem_28_38 .w-nav').find('.w-subnav');
              $this = $(this);
              item = $this.find('.w-subnav');
              item.slideDown();
          }).off('mouseleave').on('mouseleave', '.w-nav-inner', function () {
              item = $(this).find('.w-subnav');
              item.stop().slideUp();
          });
          SetNavSelectedStyle('nav_tem_28_38');//选中当前导航
      });
  </script></div></div>
          </div>
      </div>
      <div class="main-layout-wrapper" id="smv_AreaMainWrapper" style="background-color: transparent; background-image: none;
           background-repeat: no-repeat;background-position:0 0; background:-moz-linear-gradient(top, none, none);background:-webkit-gradient(linear, left top, left bottom, from(none), to(none));background:-o-linear-gradient(top, none, none);background:-ms-linear-gradient(top, none, none);background:linear-gradient(top, none, none);;background-size: auto;" bgscroll="none">
          <div class="main-layout" id="tem-main-layout11" style="width: 100%;">
              <div style="display: none">
                  
              </div>
              <div class="" id="smv_MainContent" rel="mainContentWrapper" style="width: 100%; min-height: 300px; position: relative; ">
                  
                  <div class="smvWrapper" style="min-width:1200px;  position: relative; background-color: transparent; background-image: none; background-repeat: no-repeat; background:-moz-linear-gradient(top, none, none);background:-webkit-gradient(linear, left top, left bottom, from(none), to(none));background:-o-linear-gradient(top, none, none);background:-ms-linear-gradient(top, none, none);background:linear-gradient(top, none, none);;background-position:0 0;background-size:auto;" bgscroll="none"><div class="smvContainer" id="smv_Main" cpid="60150" style="min-height:400px;width:1200px;height:1812px;  position: relative; "><div id="smv_con_1_19" ctype="slideset" class="esmartMargin smartAbs " cpid="60150" cstyle="Style1" ccolor="Item0" areaid="" iscontainer="True" pvid="" tareaid="" re-direction="y" daxis="Y" isdeletable="True" style="height: 750px; width: 100%; left: 0px; top: -110px;z-index:1;" selectarea="Area3853"><div class="yibuFrameContent con_1_19  slideset_Style1  " style="overflow:visible;;">
  <!--w-slide-->
<div id="lider_smv_con_1_19_wrapper">
    <div class="w-slide" id="slider_smv_con_1_19" data-jssor-slider="1"
        style="left: -292px; width: 1784px; visibility: visible; height: 750px;">
        <div style="position: absolute; display: block; top: 0px; left: 0px; width: 1784px; height: 750px;">
            <div style="position: absolute; display: block; top: 0px; left: 0px; width: 1784px; height: 750px;"
                data-scale-ratio="1">
                <div class="w-slide-inner" data-u="slides"
                    style="width: 1784px; z-index: 0; position: absolute; top: 0px; left: 0px;">
                    <div
                        style="top: 0px; left: 0px; width: 1784px; height: 750px; position: absolute; z-index: 0; pointer-events: none;">
                    </div>
                </div>
                <div class="w-slide-inner" data-u="slides"
                    style="width: 1784px; z-index: 0; position: absolute; overflow: hidden; top: 0px; left: 0px;">
                    <div
                        style="top: 0px; left: 0px; width: 1784px; height: 750px; position: absolute; background-color: rgb(0, 0, 0); opacity: 0; z-index: 0;">
                    </div>

                    <div class="content-box" data-area="Area3853"
                        style="z-index: 1; top: 0px; left: 0px; width: 1784px; height: 750px; position: absolute; overflow: hidden;">
                        <div id="smc_Area3853" cid="con_1_19" class="smAreaC slideset_AreaC"
                            style="z-index: 1; width: 1200px; position: relative; margin: 0px auto;">
                            <div id="smv_con_73_35" ctype="text"
                                smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;InX&quot;,&quot;animationName&quot;:&quot;flip&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                class="esmartMargin smartAbs animated" cpid="60150" cstyle="Style1" ccolor="Item0"
                                areaid="Area3853" iscontainer="False" pvid="con_1_19" tareaid="" re-direction="all"
                                daxis="All" isdeletable="True"
                                style="height: 76px; width: 703px; left: 243px; top: 311px; z-index: 5; opacity: 1;"
                                sm-finished="true" smexecuted="1">
                                <div class="yibuFrameContent con_73_35  text_Style1  "
                                    style="overflow: hidden; z-index: 1;">
                                    <div id="txt_con_73_35" style="height: 100%; z-index: 1;">
                                        <div class="editableContent" id="txtc_con_73_35"
                                            style="height: 100%; overflow-wrap: break-word; z-index: 1;">
                                            <p style="text-align: center; z-index: 1;"><strong style="z-index: 1;"><span
                                                        style="font-size: 38px; z-index: 1;"><span
                                                            style="color: rgb(255, 255, 255); z-index: 1;"><span
                                                                style="line-height: 2; z-index: 1;">联创自动化科技</span></span></span></strong>
                                            </p>

                                        </div>
                                    </div>
  
  <script style="z-index: 1;">
      var tables = $(' #smv_con_73_35').find('table')
      for (var i = 0; i < tables.length; i++) {
          var tab = tables[i]
          var borderWidth = $(tab).attr('border')
          if (borderWidth <= 0 || !borderWidth) {
              console.log(tab)
              $(tab).addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
          }
      }
  </script></div></div><div id="smv_con_74_45" ctype="text" smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;InX&quot;,&quot;animationName&quot;:&quot;flip&quot;,&quot;infinite&quot;:&quot;1&quot;}" class="esmartMargin smartAbs animated" cpid="60150" cstyle="Style1" ccolor="Item0" areaid="Area3853" iscontainer="False" pvid="con_1_19" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 40px; width: 703px; left: 248px; top: 408px; z-index: 5; opacity: 1;" sm-finished="true" smexecuted="1"><div class="yibuFrameContent con_74_45  text_Style1  " style="overflow: hidden; z-index: 1;"><div id="txt_con_74_45" style="height: 100%; z-index: 1;">
      <div class="editableContent" id="txtc_con_74_45" style="height: 100%; overflow-wrap: break-word; z-index: 1;">
          <p style="text-align: center; z-index: 1;"><span style="font-size: 20px; z-index: 1;"><span style="color: rgb(255, 255, 255); z-index: 1;"><span style="line-height: 1.5; z-index: 1;">诚信&nbsp;|&nbsp;合作&nbsp;|&nbsp;求实&nbsp;|&nbsp;创新&nbsp;</span></span></span></p>
  
      </div>
  </div>
  
  <script style="z-index: 1;">
      var tables = $(' #smv_con_74_45').find('table')
      for (var i = 0; i < tables.length; i++) {
          var tab = tables[i]
          var borderWidth = $(tab).attr('border')
          if (borderWidth <= 0 || !borderWidth) {
              console.log(tab)
              $(tab).addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
          }
      }
  </script></div></div>                    </div>
                      <div class="content-box-inner" style="background-image: url(&quot;images/15336713.jpg&quot;); background-position: 50% 50%; background-size: cover; z-index: 1;"></div>
  
                  <div style="top: 0px; left: 0px; width: 1784px; height: 750px; z-index: 1000; display: none;"></div></div>
          </div></div></div>
          <!-- Bullet Navigator -->
          <div style="position: absolute; display: block; right: 16px; bottom: 16px; left: 884.5px; width: 15px; height: 15px;"><div data-u="navigator" class="w-slide-btn-box  f-hide " data-autocenter="1" data-scale-ratio="1" style="width: 15px; left: 0px; height: 15px; top: 0px;">
              <!-- bullet navigator item prototype -->
              
          <div class="w-slide-btn w-slide-btnav" data-u="prototype" data-jssor-button="1" style="position: absolute; left: 0px; top: 0px;"></div></div></div>
  
          <!-- 1Arrow Navigator -->
          <div style="position: absolute; display: block; top: 375px; left: 10px; width: 0px; height: 0px;"><span data-u="arrowleft" class="w-slide-arrowl slideArrow f-hide w-slide-arrowlds" data-autocenter="2" id="left_con_1_19" data-jssor-button="1" data-scale-ratio="1" style="top: -11px; left: 0px; pointer-events: none;">
              <i class="w-itemicon mw-iconfont">넳</i>
          </span></div>
          <div style="position: absolute; display: block; top: 375px; right: 10px; width: 0px; height: 0px;"><span data-u="arrowright" class="w-slide-arrowr slideArrow f-hide w-slide-arrowrds" data-autocenter="2" id="right_con_1_19" data-jssor-button="1" data-scale-ratio="1" style="top: -11px; left: 0px; pointer-events: none;">
              <i class="w-itemicon mw-iconfont">넲</i>
          </span></div>
      </div>
  </div>
  
  <!--/w-slide-->
  <script type="text/javascript">
         var jssorCache_con_1_19  = {
              CtrlId:"con_1_19",
              SliderId: "slider_smv_con_1_19",
             Html:$("#slider_smv_con_1_19")[0].outerHTML,
      };
      var slide_con_1_19;
          var slideAnimation_con_1_19 =  function (slideIndex, fromIndex) {
              var $slideWrapper = $("#slider_smv_con_1_19 .w-slide-inner:last");
              var $fromSlide = $slideWrapper.find(".content-box:eq(" + fromIndex + ")");
              var $curSlide = $slideWrapper.find(".content-box:eq(" + slideIndex + ")");
              var $nextSlide = $slideWrapper.find(".content-box:eq(" + (slideIndex+1) + ")");
              $("#smv_con_1_19").attr("selectArea", $curSlide.attr("data-area"));
  
              $slideWrapper.find(".animated").smanimate("stop");
              $fromSlide.find(".animated").smanimate("stop");
              $curSlide.find(".animated").smanimate("stop");
              $nextSlide.find(".animated").smanimate("stop");
              $("#switch_con_1_19 .page").html(slideIndex + 1);
             // debugger
              $curSlide.find(".animated").smanimate("replay");
              return false;
          }
      con_1_19_page = 1;
      con_1_19_sliderset3_init = function () {
          var jssor_1_options_con_1_19 = {
              $AutoPlay: "False"=="True"?false:"on" == "on",//自动播放
              $PlayOrientation: 1,//2为向上滑，1为向左滑
              $Loop: 1,//循环
              $Idle: parseInt("4000"),//切换间隔
              $SlideDuration: "1000",//延时
              $SlideEasing: $Jease$.$OutQuint,
              
               $SlideshowOptions: {
                  $Class: $JssorSlideshowRunner$,
                  $Transitions: GetSlideAnimation("3", "1000"),
                  $TransitionsOrder: 1
              },
              
              $ArrowNavigatorOptions: {
                  $Class: $JssorArrowNavigator$
              },
              $BulletNavigatorOptions: {
                  $Class: $JssorBulletNavigator$,
                  $ActionMode: "1"
              }
          };
  
          //初始化幻灯
          var slide = slide_con_1_19 = new $JssorSlider$("slider_smv_con_1_19", jssor_1_options_con_1_19);
          if (typeof (LayoutConverter) !== "undefined") {
              jssorCache_con_1_19 .JssorOpt= jssor_1_options_con_1_19,
              jssorCache_con_1_19 .Jssor = slide;
          }
          $('#smv_con_1_19').data('jssor_slide', slide);
  
          //resize游览器的时候触发自动缩放幻灯秀
          //幻灯栏目自动或手动切换时触发的事件
          slide.$On($JssorSlider$.$EVT_PARK,slideAnimation_con_1_19);
          //切换栏点击事件
          $("#switch_con_1_19 .left").unbind("click").click(function () {
              if(con_1_19_page==1){
                  con_1_19_page =1;
              } else {
                  con_1_19_page = con_1_19_page - 1;
              }
              $("#switch_con_1_19 .page").html(con_1_19_page);
              slide.$Prev();
              return false;
          });
          $("#switch_con_1_19 .right").unbind("click").click(function () {
              if(con_1_19_page==1){
                  con_1_19_page = 1;
          } else {
          con_1_19_page = con_1_19_page + 1;
      }
      $("#switch_con_1_19 .page").html(con_1_19_page);
      slide.$Next();
      return false;
      });
      };
  
  
      $(function () {
  
          var jssorCopyTmp = document.getElementById('slider_smv_con_1_19').cloneNode(true);
  
          var $jssorIntt = function (skipInit) {
  
              //获取幻灯显示动画类型
              var $this = $('#slider_smv_con_1_19');
              var dh = $(document).height();
              var wh = $(window).height();
              var ww = $(window).width();
              var width = 1000;
              //区分页头、页尾、内容区宽度
              if ($this.parents(".header").length > 0 ) {
                  width = $this.parents(".header").width();
              } else if ($this.parents(".footer").length > 0 ){
                  width = $this.parents(".footer").width();
              } else {
                  width = $this.parents(".smvContainer").width();
              }
  
              if (ww > width) {
                  var left = parseInt((ww - width) * 10 / 2) / 10;
                  $this.css({ 'left': -left, 'width': ww });
              } else {
                  $this.css({ 'left': 0, 'width': ww });
              }
  
              //解决手机端预览PC端幻灯秀时不通栏问题
              if (VisitFromMobile() && typeof (LayoutConverter) === "undefined") {
                  $this.css("min-width", width);
                  setTimeout(function () {
                      var boxleft = (width - 330) / 2;
                      $this.find(".w-slide-btn-box").css("left", boxleft + "px");
                  }, 300);
              }
              $this.children().not(".slideArrow").css({ "width": $this.width() });
  
              if (!skipInit) {
                  con_1_19_sliderset3_init();
              }
  
  
              var areaId = $("#smv_con_1_19").attr("tareaid");
              if(areaId==""){
                  var mainWidth = $("#smv_Main").width();
                  $("#smv_con_1_19 .slideset_AreaC").css({"width":mainWidth+"px","position":"relative","margin":"0 auto"});
              }else{
                  var controlWidth = $("#smv_con_1_19").width();
                  $("#smv_con_1_19 .slideset_AreaC").css({"width":controlWidth+"px","position":"relative","margin":"0 auto"});
              }
              $("#smv_con_1_19").attr("selectArea", "Area3853");
  
              var arrowHeight = $('#slider_smv_con_1_19 .w-slide-arrowl').eq(-1).outerHeight();
              var arrowTop = (18 - arrowHeight) / 2;
              $('#slider_smv_con_1_19 .w-slide-arrowl').eq(-1).css('top', arrowTop);
              $('#slider_smv_con_1_19 .w-slide-arrowr').eq(-1).css('top', arrowTop);
          }
          //$jssorIntt();
  
              
          var ctime = null;
  
          function ScaleSlider() {
              slide_con_1_19.$Off($JssorSlider$.$EVT_PARK,slideAnimation_con_1_19);
              if (ctime) {
                  clearTimeout(ctime);
                  ctime = null;
              }
              ctime = setTimeout(function () {
                  var inst = $('#slider_smv_con_1_19');
                       var orginWidth = inst.width();
                       if (orginWidth == $(window).width()) return;
                      var inst_parent = inst.parent();
                      inst.remove()
                       inst_parent.append(jssorCopyTmp.cloneNode(true));
  
                  inst_parent.find('.animated').smanimate().stop();
                       //$('.smartRecpt').smrecompute()
  
                  $jssorIntt();
                  ctime = null;
              }, 200);
  
          }
          if (typeof (LayoutConverter) === "undefined") {
              $Jssor$.$CancelEvent(window, "resize", ScaleSlider);
              $Jssor$.$AddEvent(window, "resize", ScaleSlider);
          }
          
            if (typeof (LayoutConverter) !== "undefined") {
              jssorCache_con_1_19 .ResizeFunc = $jssorIntt;
              LayoutConverter.CtrlJsVariableList.push(jssorCache_con_1_19 );
          }
  
      });
  </script>
  </div></div><div id="smv_con_41_37" ctype="banner" class="esmartMargin smartAbs " cpid="60150" cstyle="Style3" ccolor="Item0" areaid="" iscontainer="True" pvid="" tareaid="" re-direction="y" daxis="Y" isdeletable="True" style="height: 652px; width: 100%; left: 0px; top: 715px;z-index:1;"><div class="yibuFrameContent con_41_37  banner_Style3  " style="overflow:visible;;">
  <div class="w-banner">
      <div class="w-banner-content fullcolumn-inner smAreaC" id="smc_Area0" cid="con_41_37" style="width:1200px">
          <div id="smv_con_42_37" ctype="area" smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Down&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}" class="esmartMargin smartAbs animated" cpid="60150" cstyle="Style1" ccolor="Item1" areaid="Area0" iscontainer="True" pvid="con_41_37" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 104px; width: 518px; left: 341px; top: 34px; z-index: 2; opacity: 1;" sm-finished="true" smexecuted="1"><div class="yibuFrameContent con_42_37  area_Style1  " style="overflow:visible;;"><div class="w-container" data-effect-name="enterTop">
      <div class="smAreaC" id="smc_Area0" cid="con_42_37">
          <div id="smv_con_45_37" ctype="line" class="esmartMargin smartAbs " cpid="60150" cstyle="Style1" ccolor="Item3" areaid="Area0" iscontainer="False" pvid="con_42_37" tareaid="" re-direction="x" daxis="All" isdeletable="True" style="height: 20px; width: 35px; left: 241px; top: 75px;z-index:4;"><div class="yibuFrameContent con_45_37  line_Style1  " style="overflow:visible;;"><!-- w-line -->
  <div style="position:relative; height:100%">
      <div class="w-line" style="position:absolute;top:50%;" linetype="horizontal"></div>
  </div>
  </div></div><div id="smv_con_44_37" ctype="text" class="esmartMargin smartAbs " cpid="60150" cstyle="Style1" ccolor="Item3" areaid="Area0" iscontainer="False" pvid="con_42_37" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 20px; width: 360px; left: 79px; top: 59px;z-index:3;"><div class="yibuFrameContent con_44_37  text_Style1  " style="overflow:hidden;;"><div id="txt_con_44_37" style="height: 100%;">
      <div class="editableContent" id="txtc_con_44_37" style="height: 100%; word-wrap:break-word;">
          <p style="text-align:center"><span style="line-height:1.5"><span style="font-family:Arial,Helvetica,sans-serif"><span style="color:#cfd9d5">Product service</span></span></span></p>
  
      </div>
  </div>
  
  <script>
      var tables = $(' #smv_con_44_37').find('table')
      for (var i = 0; i < tables.length; i++) {
          var tab = tables[i]
          var borderWidth = $(tab).attr('border')
          if (borderWidth <= 0 || !borderWidth) {
              console.log(tab)
              $(tab).addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
          }
      }
  </script></div></div><div id="smv_con_43_37" ctype="text" class="esmartMargin smartAbs " cpid="60150" cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_42_37" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 62px; width: 276px; left: 121px; top: 11px;z-index:2;"><div class="yibuFrameContent con_43_37  text_Style1  " style="overflow:hidden;;"><div id="txt_con_43_37" style="height: 100%;">
      <div class="editableContent" id="txtc_con_43_37" style="height: 100%; word-wrap:break-word;">
          <p style="text-align:center"><span style="color:#333333"><span style="font-size:36px">产品服务</span></span></p>
  
      </div>
  </div>
  
  <script>
      var tables = $(' #smv_con_43_37').find('table')
      for (var i = 0; i < tables.length; i++) {
          var tab = tables[i]
          var borderWidth = $(tab).attr('border')
          if (borderWidth <= 0 || !borderWidth) {
              console.log(tab)
              $(tab).addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
          }
      }
  </script></div></div>    </div>
  </div></div></div><div id="smv_con_46_37" ctype="multicolumn" class="esmartMargin smartAbs " cpid="60150" cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="True" pvid="con_41_37" tareaid="" re-direction="y" daxis="Y" isdeletable="True" style="height: 490px; width: 100%; left: 0px; top: 161px;z-index:3;" selectarea="columnArea0"><div class="yibuFrameContent con_46_37  multicolumn_Style1  " style="overflow:visible;;">
  <div class="w-columns " id="mc_con_46_37" data-spacing="0" data-pagewidth="1200" style="width: 1200px;">
      <ul class="w-columns-inner">
              <li class="w-columns-item" data-area="columnArea0" data-width="33">
                  <div class="w-columns-interval">
                      <div class="w-columns-content" style="background-color: rgb(243, 243, 243); background-image: none; background-repeat: repeat; background-position: 50% 50%; background: -moz-linear-gradient(top, none, none);background: -ms-linear-gradient(none, none);background: -webkit-gradient(linear, left top, left bottom, from(none), to(none));background: -o-linear-gradient(top, none, none);background: linear-gradient(top, none, none);background-size:auto;">
                          <div class="w-columns-content-inner smAreaC" id="smc_columnArea0" cid="con_46_37" style="width:396px;">
                              <div id="smv_con_47_37" ctype="area" smanim="{&quot;delay&quot;:0.5,&quot;duration&quot;:0.5,&quot;direction&quot;:&quot;Up&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}" class="esmartMargin smartAbs animated" cpid="60150" cstyle="Style1" ccolor="Item1" areaid="columnArea0" iscontainer="True" pvid="con_46_37" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 460px; width: 380px; left: 8px; top: 10px; z-index: 3; opacity: 1;" sm-finished="true" smexecuted="1"><div class="yibuFrameContent con_47_37  area_Style1  " style="overflow:visible;;"><div class="w-container" data-effect-name="enterTop">
      <div class="smAreaC" id="smc_Area0" cid="con_47_37">
          <div id="smv_con_50_37" ctype="text" class="esmartMargin smartAbs " cpid="60150" cstyle="Style1" ccolor="Item3" areaid="Area0" iscontainer="False" pvid="con_47_37" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 93px; width: 360px; left: 10px; top: 360px;z-index:4;"><div class="yibuFrameContent con_50_37  text_Style1  " style="overflow:hidden;;"><div id="txt_con_50_37" style="height: 100%;">  </div>
  
  <script>
      var tables = $(' #smv_con_50_37').find('table')
      for (var i = 0; i < tables.length; i++) {
          var tab = tables[i]
          var borderWidth = $(tab).attr('border')
          if (borderWidth <= 0 || !borderWidth) {
              console.log(tab)
              $(tab).addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
          }
      }
  </script></div></div><div id="smv_con_49_37" ctype="text" class="esmartMargin smartAbs " cpid="60150" cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_47_37" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 40px; width: 236px; left: 54px; top: 300px;z-index:3;"><div class="yibuFrameContent con_49_37  text_Style1  " style="overflow:hidden;;"><div id="txt_con_49_37" style="height: 100%;">
      <div class="editableContent" id="txtc_con_49_37" style="height: 100%; word-wrap:break-word;">
          <p style="text-align:center"><span style="line-height:1.2"><span style="color:#333333"><span style="font-family:Arial,Helvetica,sans-serif"><span style="font-size:30px">AI平台软件</span></span></span></span></p>
  
      </div>
  </div>
  
  <script>
      var tables = $(' #smv_con_49_37').find('table')
      for (var i = 0; i < tables.length; i++) {
          var tab = tables[i]
          var borderWidth = $(tab).attr('border')
          if (borderWidth <= 0 || !borderWidth) {
              console.log(tab)
              $(tab).addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
          }
      }
  </script></div></div><div id="smv_con_70_3" ctype="image" class="esmartMargin smartAbs " cpid="60150" cstyle="Style3" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_47_37" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 277px; width: 370px; left: 5px; top: 0px;z-index:6;"><div class="yibuFrameContent con_70_3  image_Style3  " style="overflow:visible;;">
  <div class="w-image-box" data-filltype="1" id="div_con_70_3" style="height: 277px;">
      <a target="_self" href="smartAI.html">
          <img loading="lazy" src="images/15336747.jpg" alt="" title="" id="img_smv_con_70_3" style="width: auto; height: 277px; margin-left: -0.666667px; margin-top: 0px;">
      </a>
  </div>
  
  <script type="text/javascript">
      $(function () {
          InitImageSmv("con_70_3","368","277","1");
              
          $('#smv_con_70_3 .w-image-box').hover(function () {
              $(this).find("img").css({ "transform": "scale(1.3)", "transition": "all,.4s" })
          }, function () {
              $(this).find("img").css({ "transform": "", "transition": "" })
          });
              
      });
  </script></div></div>    </div>
  </div></div></div>                                                    </div>
                      </div>
                  </div>
              </li>
              <li class="w-columns-item" data-area="columnArea1" data-width="33">
                  <div class="w-columns-interval">
                      <div class="w-columns-content" style="background-color: rgb(238, 238, 238); background-image: none; background-repeat: repeat; background-position: 50% 50%; background: -moz-linear-gradient(top, none, none);background: -ms-linear-gradient(none, none);background: -webkit-gradient(linear, left top, left bottom, from(none), to(none));background: -o-linear-gradient(top, none, none);background: linear-gradient(top, none, none);background-size:auto;">
                          <div class="w-columns-content-inner smAreaC" id="smc_columnArea1" cid="con_46_37" style="width:396px;">
                              <div id="smv_con_52_37" ctype="area" smanim="{&quot;delay&quot;:0.6,&quot;duration&quot;:0.6,&quot;direction&quot;:&quot;Up&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}" class="esmartMargin smartAbs animated" cpid="60150" cstyle="Style1" ccolor="Item1" areaid="columnArea1" iscontainer="True" pvid="con_46_37" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 460px; width: 380px; left: 8px; top: 10px; z-index: 3; opacity: 1;" sm-finished="true" smexecuted="1"><div class="yibuFrameContent con_52_37  area_Style1  " style="overflow:visible;;"><div class="w-container" data-effect-name="enterTop">
      <div class="smAreaC" id="smc_Area0" cid="con_52_37">
          <div id="smv_con_54_37" ctype="text" class="esmartMargin smartAbs " cpid="60150" cstyle="Style1" ccolor="Item3" areaid="Area0" iscontainer="False" pvid="con_52_37" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 85px; width: 360px; left: 10px; top: 360px;z-index:4;"><div class="yibuFrameContent con_54_37  text_Style1  " style="overflow:hidden;;"><div id="txt_con_54_37" style="height: 100%;">
      <div class="editableContent" id="txtc_con_54_37" style="height: 100%; word-wrap:break-word;">  
      </div>
  </div>
  
  <script>
      var tables = $(' #smv_con_54_37').find('table')
      for (var i = 0; i < tables.length; i++) {
          var tab = tables[i]
          var borderWidth = $(tab).attr('border')
          if (borderWidth <= 0 || !borderWidth) {
              console.log(tab)
              $(tab).addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
          }
      }
  </script></div></div><div id="smv_con_55_37" ctype="text" class="esmartMargin smartAbs " cpid="60150" cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_52_37" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 40px; width: 245px; left: 75px; top: 300px;z-index:3;"><div class="yibuFrameContent con_55_37  text_Style1  " style="overflow:hidden;;"><div id="txt_con_55_37" style="height: 100%;">
      <div class="editableContent" id="txtc_con_55_37" style="height: 100%; word-wrap:break-word;">
          <p style="text-align:center"><span style="line-height:1.2"><span style="color:#333333"><span style="font-family:Arial,Helvetica,sans-serif"><span style="font-size:30px">智能硬件</span></span></span></span></p>
  
      </div>
  </div>
  
  <script>
      var tables = $(' #smv_con_55_37').find('table')
      for (var i = 0; i < tables.length; i++) {
          var tab = tables[i]
          var borderWidth = $(tab).attr('border')
          if (borderWidth <= 0 || !borderWidth) {
              console.log(tab)
              $(tab).addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
          }
      }
  </script></div></div><div id="smv_con_71_28" ctype="image" class="esmartMargin smartAbs " cpid="60150" cstyle="Style3" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_52_37" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 277px; width: 370px; left: 5px; top: 0px;z-index:6;"><div class="yibuFrameContent con_71_28  image_Style3  " style="overflow:visible;;">
  <div class="w-image-box" data-filltype="1" id="div_con_71_28" style="height: 277px;">
      <a target="_self" href="kyList.html">
          <img loading="lazy" src="images/15336749.jpg" alt="" title="" id="img_smv_con_71_28" style="width: auto; height: 277px; margin-left: -0.711058px; margin-top: 0px;">
      </a>
  </div>
  
  <script type="text/javascript">
      $(function () {
          InitImageSmv("con_71_28","368","277","1");
              
          $('#smv_con_71_28 .w-image-box').hover(function () {
              $(this).find("img").css({ "transform": "scale(1.3)", "transition": "all,.4s" })
          }, function () {
              $(this).find("img").css({ "transform": "", "transition": "" })
          });
              
      });
  </script></div></div>    </div>
  </div></div></div>                                                    </div>
                      </div>
                  </div>
              </li>
              <li class="w-columns-item" data-area="columnArea2" data-width="34">
                  <div class="w-columns-interval">
                      <div class="w-columns-content" style="background-color: rgb(243, 243, 243); background-image: none; background-repeat: repeat; background-position: 50% 50%; background: -moz-linear-gradient(top, none, none);background: -ms-linear-gradient(none, none);background: -webkit-gradient(linear, left top, left bottom, from(none), to(none));background: -o-linear-gradient(top, none, none);background: linear-gradient(top, none, none);background-size:auto;">
                          <div class="w-columns-content-inner smAreaC" id="smc_columnArea2" cid="con_46_37" style="width:408px;">
                              <div id="smv_con_57_37" ctype="area" smanim="{&quot;delay&quot;:0.7,&quot;duration&quot;:0.7,&quot;direction&quot;:&quot;Up&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}" class="esmartMargin smartAbs animated" cpid="60150" cstyle="Style1" ccolor="Item1" areaid="columnArea2" iscontainer="True" pvid="con_46_37" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 460px; width: 380px; left: 14px; top: 10px; z-index: 3; opacity: 1;" sm-finished="true" smexecuted="1"><div class="yibuFrameContent con_57_37  area_Style1  " style="overflow:visible;;"><div class="w-container" data-effect-name="enterTop">
      <div class="smAreaC" id="smc_Area0" cid="con_57_37">
          <div id="smv_con_72_7" ctype="image" class="esmartMargin smartAbs " cpid="60150" cstyle="Style3" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_57_37" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 277px; width: 370px; left: 5px; top: 0px;z-index:6;"><div class="yibuFrameContent con_72_7  image_Style3  " style="overflow:visible;;">
  <div class="w-image-box" data-filltype="1" id="div_con_72_7" style="height: 277px;">
      <a target="_self" href="sovleWay.html">
          <img loading="lazy" src="images/15336750.png" alt="" title="" id="img_smv_con_72_7" style="width: auto; height: 277px; margin-left: -0.666667px; margin-top: 0px;">
      </a>
  </div>
  
  <script type="text/javascript">
      $(function () {
          InitImageSmv("con_72_7","368","277","1");
              
          $('#smv_con_72_7 .w-image-box').hover(function () {
              $(this).find("img").css({ "transform": "scale(1.3)", "transition": "all,.4s" })
          }, function () {
              $(this).find("img").css({ "transform": "", "transition": "" })
          });
              
      });
  </script></div></div><div id="smv_con_59_37" ctype="text" class="esmartMargin smartAbs " cpid="60150" cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_57_37" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 40px; width: 255px; left: 66px; top: 300px;z-index:3;"><div class="yibuFrameContent con_59_37  text_Style1  " style="overflow:hidden;;"><div id="txt_con_59_37" style="height: 100%;">
      <div class="editableContent" id="txtc_con_59_37" style="height: 100%; word-wrap:break-word;">
          <p style="text-align:center"><span style="line-height:1.2"><span style="color:#333333"><span style="font-family:Arial,Helvetica,sans-serif"><span style="font-size:30px">系统解决方案</span></span></span></span></p>
  
      </div>
  </div>
  
  <script>
      var tables = $(' #smv_con_59_37').find('table')
      for (var i = 0; i < tables.length; i++) {
          var tab = tables[i]
          var borderWidth = $(tab).attr('border')
          if (borderWidth <= 0 || !borderWidth) {
              console.log(tab)
              $(tab).addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
          }
      }
  </script></div></div><div id="smv_con_60_37" ctype="text" class="esmartMargin smartAbs " cpid="60150" cstyle="Style1" ccolor="Item3" areaid="Area0" iscontainer="False" pvid="con_57_37" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 89px; width: 360px; left: 10px; top: 360px;z-index:4;"><div class="yibuFrameContent con_60_37  text_Style1  " style="overflow:hidden;;"><div id="txt_con_60_37" style="height: 100%;">
      <div class="editableContent" id="txtc_con_60_37" style="height: 100%; word-wrap:break-word;">
  
      </div>
  </div>
  
  <script>
      var tables = $(' #smv_con_60_37').find('table')
      for (var i = 0; i < tables.length; i++) {
          var tab = tables[i]
          var borderWidth = $(tab).attr('border')
          if (borderWidth <= 0 || !borderWidth) {
              console.log(tab)
              $(tab).addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
          }
      }
  </script></div></div>    </div>
  </div></div></div>                                                    </div>
                      </div>
                  </div>
              </li>
      </ul>
  </div>
  <script type="text/javascript">
      $(function () {
          $("#mc_con_46_37>ul >li.w-columns-item").hover(function () {
              $("#smv_con_46_37").attr("selectArea", $(this).attr("data-area"));
          });
          $("#smv_con_46_37").attr("selectArea", "columnArea0");
      });
  </script></div></div>    </div>
      <div class="w-banner-color fullcolumn-outer" id="bannerWrap_con_41_37" style="left: -292px; width: 1784px;">
          <div class="w-banner-image" style="background-attachment: fixed;"></div>
      </div>
  </div>
  
  <script type="text/javascript">
  
      $(function () {
          var resize = function () {
              $("#smv_con_41_37 >.yibuFrameContent>.w-banner>.fullcolumn-inner").width($("#smv_con_41_37").parent().width());
              $('#bannerWrap_con_41_37').fullScreen(function (t) {
                  if (VisitFromMobile()) {
                      t.css("min-width", t.parent().width());
                  }
              
                  $("#bannerWrap_con_41_37 > .w-banner-image").lzparallax({ effect:'fixed' ,autoPosition:false});
              
              });
          }
          if (typeof (LayoutConverter) !== "undefined") {
              LayoutConverter.CtrlJsVariableList.push({
                  CtrlId: "con_41_37",
                  ResizeFunc: resize,
              });
          }
          else {
              $(window).resize(function (e) {
                  if (e.target == this) {
                      resize();
                  }
              });
          }
          
          resize();
      });
  </script>
  </div></div><div id="smv_con_78_16" ctype="area" smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Down&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}" class="esmartMargin smartAbs animated" cpid="60150" cstyle="Style1" ccolor="Item1" areaid="Main" iscontainer="True" pvid="" tareaid="Main" re-direction="all" daxis="All" isdeletable="True" style="height: 104px; width: 518px; left: 321px; top: 1399px; z-index: 2; opacity: 1;" sm-finished="true" smexecuted="1"><div class="yibuFrameContent con_78_16  area_Style1  " style="overflow:visible;;"><div class="w-container" data-effect-name="enterTop">
      <div class="smAreaC" id="smc_Area0" cid="con_78_16">
          <div id="smv_con_81_16" ctype="text" class="esmartMargin smartAbs " cpid="60150" cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_78_16" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 62px; width: 276px; left: 119px; top: 17px;z-index:2;"><div class="yibuFrameContent con_81_16  text_Style1  " style="overflow:hidden;;"><div id="txt_con_81_16" style="height: 100%;">
      <div class="editableContent" id="txtc_con_81_16" style="height: 100%; word-wrap:break-word;">
          <p style="text-align:center"><span style="color:#333333"><span style="font-size:36px">合作伙伴</span></span></p>
  
      </div>
  </div>
  
  <script>
      var tables = $(' #smv_con_81_16').find('table')
      for (var i = 0; i < tables.length; i++) {
          var tab = tables[i]
          var borderWidth = $(tab).attr('border')
          if (borderWidth <= 0 || !borderWidth) {
              console.log(tab)
              $(tab).addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
          }
      }
  </script></div></div><div id="smv_con_80_16" ctype="text" class="esmartMargin smartAbs " cpid="60150" cstyle="Style1" ccolor="Item3" areaid="Area0" iscontainer="False" pvid="con_78_16" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 20px; width: 360px; left: 79px; top: 59px;z-index:3;"><div class="yibuFrameContent con_80_16  text_Style1  " style="overflow:hidden;;"><div id="txt_con_80_16" style="height: 100%;">
      <div class="editableContent" id="txtc_con_80_16" style="height: 100%; word-wrap:break-word;">
          <p style="text-align:center">Partners</p>
      </div>
  </div>
  
  <script>
      var tables = $(' #smv_con_80_16').find('table')
      for (var i = 0; i < tables.length; i++) {
          var tab = tables[i]
          var borderWidth = $(tab).attr('border')
          if (borderWidth <= 0 || !borderWidth) {
              console.log(tab)
              $(tab).addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
          }
      }
  </script></div></div><div id="smv_con_79_16" ctype="line" class="esmartMargin smartAbs " cpid="60150" cstyle="Style1" ccolor="Item3" areaid="Area0" iscontainer="False" pvid="con_78_16" tareaid="" re-direction="x" daxis="All" isdeletable="True" style="height: 20px; width: 690px; left: -77px; top: 75px;z-index:4;"><div class="yibuFrameContent con_79_16  line_Style1  " style="overflow:visible;;"><!-- w-line -->
  <div style="position:relative; height:100%">
      <div class="w-line" style="position:absolute;top:50%;" linetype="horizontal"></div>
  </div>
  </div></div>    </div>
  </div></div></div>

</div></div>
              </div>
          </div>
      </div>
      <div style="background-color: rgb(33, 37, 46); background-image: none; background-repeat: no-repeat;background-position:0 0; background:-moz-linear-gradient(top, none, none);background:-webkit-gradient(linear, left top, left bottom, from(none), to(none));background:-o-linear-gradient(top, none, none);background:-ms-linear-gradient(top, none, none);background:linear-gradient(top, none, none);;
           position: relative; width: 100%;min-width:1200px;background-size: auto;" bgscroll="none">
          <div class=" footer" cpid="60135" id="smv_Area3" style="width: 1200px; height: 130px; position: relative; margin: 0 auto;">
              <div id="smv_tem_5_11" ctype="banner" class="esmartMargin smartAbs " cpid="60135" cstyle="Style1" ccolor="Item0" areaid="Area3" iscontainer="True" pvid="" tareaid="Area3" re-direction="y" daxis="Y" isdeletable="True" style="height: 130px; width: 100%; left: 0px; top: 0px;z-index:2;"><div class="yibuFrameContent tem_5_11  banner_Style1  " style="overflow:visible;;"><div class="fullcolumn-inner smAreaC" id="smc_Area0" cid="tem_5_11" style="width:1200px">
      <div id="smv_tem_35_16" ctype="text" class="esmartMargin smartAbs " cpid="60135" cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="tem_5_11" tareaid="Area3" re-direction="all" daxis="All" isdeletable="True" style="height: 84px; width: 415px; left: 43px; top: 29px;z-index:11;"><div class="yibuFrameContent tem_35_16  text_Style1  " style="overflow:hidden;;"><div id="txt_tem_35_16" style="height: 100%;">
    <div class="editableContent" id="txtc_tem_35_16" style="height: 100%; word-wrap:break-word;">
        <p><span style="line-height:2"><span style="color:#ffffff"><span style="font-family:Microsoft YaHei"><span
                            style="font-size:14px">邮箱：<EMAIL>&nbsp; 电话：李经理14752221901<br>
                            徐州高新技术产业开发区漓江路15号徐州国家安全科技产业园B5#-1-205
                            </span></span></span></span></p>
    
    </div>
  </div>
  
  <script>
      var tables = $(' #smv_tem_35_16').find('table')
      for (var i = 0; i < tables.length; i++) {
          var tab = tables[i]
          var borderWidth = $(tab).attr('border')
          if (borderWidth <= 0 || !borderWidth) {
              console.log(tab)
              $(tab).addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
              $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
              $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
          }
      }
  </script></div></div></div>
  <div id="bannerWrap_tem_5_11" class="fullcolumn-outer" style="position: absolute; top: 0px; bottom: 0px; left: -292px; width: 1784px;">
  </div>
  
  <script type="text/javascript">
  
      $(function () {
          var resize = function () {
              $("#smv_tem_5_11 >.yibuFrameContent>.fullcolumn-inner").width($("#smv_tem_5_11").parent().width());
              $('#bannerWrap_tem_5_11').fullScreen(function (t) {
                  if (VisitFromMobile()) {
                      t.css("min-width", t.parent().width())
                  }
              });
          }
          if (typeof (LayoutConverter) !== "undefined") {
              LayoutConverter.CtrlJsVariableList.push({
                  CtrlId: "tem_5_11",
                  ResizeFunc: resize,
              });
          } else {
              $(window).resize(function (e) {
                  if (e.target == this) {
                      resize();
                  }
              });
          }
  
          resize();
      });
  </script>
  </div></div>
  <div id="smv_tem_36_17" ctype="image" class="esmartMargin smartAbs " cpid="60135" cstyle="Style1" ccolor="Item0" areaid="Area3" iscontainer="False" pvid="" tareaid="Area3" re-direction="all" daxis="All" isdeletable="True" style="height: 25px; width: 25px; left: 10px; top: 30px;z-index:12;"><div class="yibuFrameContent tem_36_17  image_Style1  " style="overflow:visible;;">
      <div class="w-image-box image-clip-wrap" data-filltype="1" id="div_tem_36_17" style="height: 25px;">
          <a target="_self">
              <img loading="lazy" src="images/15444447.png" alt="邮箱" title="" id="img_smv_tem_36_17" style="width: auto; height: 25px; margin-left: -1px; margin-top: 0px;" class="">
          </a>
      </div>
  
      <script type="text/javascript">
          $(function () {
              
                  InitImageSmv("tem_36_17", "23", "25", "1");
              
                   });
      </script>
  
  </div></div><div id="smv_tem_37_58" ctype="image" class="esmartMargin smartAbs " cpid="60135" cstyle="Style1" ccolor="Item0" areaid="Area3" iscontainer="False" pvid="" tareaid="Area3" re-direction="all" daxis="All" isdeletable="True" style="height: 25px; width: 25px; left: 10px; top: 58px;z-index:12;"><div class="yibuFrameContent tem_37_58  image_Style1  " style="overflow:visible;;">
      <div class="w-image-box image-clip-wrap" data-filltype="0" id="div_tem_37_58" style="height: 25px; width: 25px;">
          <a target="_self">
              <img loading="lazy" src="images/15444503.png" alt="定位" title="" id="img_smv_tem_37_58" style="width: 25px; height: 25px;" class="">
          </a>
      </div>
  
      <script type="text/javascript">
          $(function () {
              
                  InitImageSmv("tem_37_58", "23", "25", "0");
              
                   });
      </script>
  
  </div></div><div id="smv_tem_40_31" ctype="code" class="esmartMargin smartAbs " cpid="60135" cstyle="Style1" ccolor="Item0" areaid="Area3" iscontainer="False" pvid="" tareaid="Area3" re-direction="all" daxis="All" isdeletable="True" style="height: 20px; width: 95px; left: 836px; top: 23px;z-index:0;"><div class="yibuFrameContent tem_40_31  code_Style1  " style="overflow:hidden;;"><!--w-code-->
  <div class="w-code">
      <div id="code_tem_40_31"><script>
  setTimeout(function(){
  $("img").attr("title","")},10)
  </script></div>
  </div>
  <!--/w-code--></div></div>
          </div>
      </div>
  </div>
      <script type="text/javascript">
            $(document.body).bind('contextmenu', function () { return false; });
      </script>
  
  <!--w-cs-->

  <div id="smv_tem_13_34" ctype="qqservice" class="esmartMargin smartAbs smartFixed    exist" cpid="60135" cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="" tareaid="Area0" re-direction="x" daxis="All" isdeletable="True" style="height: 61px; width: 100px; right: 0px; bottom: 0px;z-index:6;"><div class="yibuFrameContent tem_13_34  qqservice_Style1  " style="overflow:hidden;;">
    <!--w-cs-->
    <div class="tem_13_34_c w-cs" id="qqservice_tem_13_34">
        <ul class="w-cs-btn">
            <li class="w-cs-list w-cs-upBtn ">
                <a href="javascript:void(0);" onclick="gotoTop();return false;" class="w-cs-icon"><i class="iconfont icon-direction-up"></i></a>
            </li>
        </ul>
    </div>
    <!--/w-cs-->
    <script>
    
        $(function () {
            var sv = $("#qqservice_tem_13_34");
    
            var numbers =[];
            $.each(sv.find(".w-cs-menu"), function() { numbers.push(this.scrollWidth); });
            var maxInNumbers = Math.max.apply(Math, numbers);
    
            sv.find(".w-cs-menu").css("width", maxInNumbers + "px");
            //  显示
            sv.find(".w-cs-list").hover(function () {
                $(this).find("ul.w-cs-menu").stop().animate({ right: 61 }, 200);
            }, function () {
                $(this).find("ul.w-cs-menu").stop().animate({ right: "0" }, 200);
            });
                
                    $("#smv_tem_13_34").addClass('exist').appendTo($('body'));
                
        });
        function gotoTop(acceleration, stime) {
            acceleration = acceleration || 0.1;
            stime = stime || 10;
            var x1 = 0;
            var y1 = 0;
            var x2 = 0;
            var y2 = 0;
            if (document.documentElement) {
                x1 = document.documentElement.scrollLeft || 0;
                y1 = document.documentElement.scrollTop || 0;
            }
            if (document.body) {
                x2 = document.body.scrollLeft || 0;
                y2 = document.body.scrollTop || 0;
            }
            var x3 = window.scrollX || 0;
            var y3 = window.scrollY || 0;
    
            // 滚动条到页面顶部的水平距离
            var x = Math.max(x1, Math.max(x2, x3));
            // 滚动条到页面顶部的垂直距离
            var y = Math.max(y1, Math.max(y2, y3));
    
            // 滚动距离 = 目前距离 / 速度, 因为距离原来越小, 速度是大于 1 的数, 所以滚动距离会越来越小
            var speeding = 1 + acceleration;
            window.scrollTo(Math.floor(x / speeding), Math.floor(y / speeding));
    
            // 如果距离不为零, 继续调用函数
            if (x > 0 || y > 0) {
                var run = "gotoTop(" + acceleration + ", " + stime + ")";
                window.setTimeout(run, stime);
            }
    
            if (typeof (LayoutConverter) !== "undefined" && typeof (CtrlAdjuster) !== "undefined" && CtrlAdjuster.IsMobile)
            {
                $("#qqservice_tem_13_34").trigger("mouseout");
            }
        }
    </script></div></div>
    </body></html>