<html>

<head>
    <meta name="viewport" content="width=device-width">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description">
    <meta name="renderer" content="webkit">
    <meta name="applicable-device" content="pc">
    <meta http-equiv="Cache-Control" content="no-transform">
    <link href="testPages/Designer/Content/bottom/pcstyle.css?_version=20230608105041" rel="stylesheet" type="text/css">
    <link href="testPages/Content/public/css/reset.css?_version=20230608105041" rel="stylesheet" type="text/css">
    <link href="testPages/Designer/Content/base/css/pager.css?_version=20230608105041" rel="stylesheet" type="text/css">
    <link href="testPages/Designer/Content/base/css/hover-effects.css?_version=20230608105041" rel="stylesheet"
        type="text/css">
    <link href="testPages/Designer/Content/base/css/antChain.css?_version=20230608105041" rel="stylesheet"
        type="text/css">
        <link href="testPages/static/iconfont/font_somdr6xou4/iconfont.css" rel="stylesheet" />
        <link href="testPages/pubsf/10197/10197361/css/60478_Pc_zh-CN.css?preventCdnCacheSeed=4381bb0dcb0c419db6a82476bc0db30b" rel="stylesheet">
    <script src="testPages/Scripts/JQuery/jquery-3.6.3.min.js?_version=20230608105042" type="text/javascript"></script>
    <script src="testPages/Designer/Scripts/jquery.lazyload.min.js?_version=20230608105042"
        type="text/javascript"></script>
    <script src="testPages/Designer/Scripts/smart.animation.min.js?_version=20230608105042"
        type="text/javascript"></script>
    <script src="testPages/Designer/Content/Designer-panel/js/kino.razor.min.js?_version=20230608105041"
        type="text/javascript"></script>
    <script src="testPages/Scripts/common.min.js?v=20200318&amp;_version=20230608105042"
        type="text/javascript"></script>
    <script src="testPages/Administration/Scripts/admin.validator.min.js?_version=20230608105035"
        type="text/javascript"></script>
    <script src="testPages/Administration/Content/plugins/cookie/jquery.cookie.js?_version=20230608105034"
        type="text/javascript"></script>
    <script type="text/javascript" id="jssor-all"
        src="testPages/Designer/Scripts/jssor.slider-22.2.16-all.min.js?_version=20230608105042"></script>
    <script type="text/javascript" id="slideshown"
        src="testPages/Designer/Scripts/slideshow.js?_version=20230608105042"></script>
    <script type="text/javascript" id="lzparallax" src="testPages/static/lzparallax/1.0.0/lz-parallax.min.js"></script>
    <script type="text/javascript" id="SuperSlide"
        src="testPages/Designer/Content/Designer-panel/js/jquery.SuperSlide.2.1.1.js"></script>
    <script type="text/javascript" id="jqPaginator" src="testPages/Scripts/statics/js/jqPaginator.min.js"></script>
    <script type="text/javascript" id="lz-slider" src="testPages/Scripts/statics/js/lz-slider.min.js"></script>
    <script type="text/javascript" id="lz-preview" src="testPages/Scripts/statics/js/lz-preview.min.js"></script>
    <title>徐州联创自动化科技有限公司</title>
    <link id="lz-preview-css" href="testPages/Content/css/atlas-preview.css" rel="stylesheet">
</head>

<body id="smart-body" area="main">

    <script type="text/javascript">
        $(function () {

            if ("False" == "True") {
                $('#mainContentWrapper').addClass('translate');
                $('#antChainWrap').fadeIn(500);

                $('#closeAntChain').off('click').on('click', function () {
                    $('#antChainWrap').fadeOut('slow', function () {
                        $('#mainContentWrapper').removeClass('translate');
                    });
                    $(document).off("scroll", isWatchScroll);

                });
                $('#showQrcodeBtn').off('click').on('click', function () {
                    $('#qrCodeWrappper').toggleClass('qrCodeShow');
                });
                $(document).scroll(isWatchScroll)
            }


            function isWatchScroll() {
                var scroH = $(document).scrollTop();
                if (scroH >= 80) {
                    $('#mainContentWrapper').removeClass('translate');
                } else {
                    $('#mainContentWrapper').addClass('translate');
                }
            }


        })
    </script>



    <div id="mainContentWrapper" style="background-color: transparent; background-image: none; background-repeat: no-repeat;background-position:0 0; background:-moz-linear-gradient(top, none, none);background:-webkit-gradient(linear, left top, left bottom, from(none), to(none));background:-o-linear-gradient(top, none, none);background:-ms-linear-gradient(top, none, none);background:linear-gradient(top, none, none);;
         position: relative; width: 100%;min-width:1200px;background-size: auto;" bgscroll="none">
        <div style="background-color: transparent; background-image: none; background-repeat: no-repeat;background-position:0 0; background:-moz-linear-gradient(top, none, none);background:-webkit-gradient(linear, left top, left bottom, from(none), to(none));background:-o-linear-gradient(top, none, none);background:-ms-linear-gradient(top, none, none);background:linear-gradient(top, none, none);;
                position: relative; width: 100%;min-width:1200px;background-size: auto;" bgscroll="none">
            <div class=" header" cpid="60135" id="smv_Area0"
                style="width: 1200px; height: 70px;  position: relative; margin: 0 auto">
                <div id="smv_tem_1_45" ctype="banner" class="esmartMargin smartAbs smartFixed   " cpid="60135"
                    cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="True" pvid="" tareaid="Area0"
                    re-direction="y" daxis="Y" isdeletable="True"
                    style="height: 70px; width: 100%; left: 0px; top: 0px;right:0px;margin:auto;z-index:13;">
                    <div class="yibuFrameContent tem_1_45  banner_Style1  " style="overflow:visible;;">
                        <div class="fullcolumn-inner smAreaC" id="smc_Area0" cid="tem_1_45" style="width:1200px"></div>
                        <div id="bannerWrap_tem_1_45" class="fullcolumn-outer"
                            style="position: absolute; top: 0px; bottom: 0px; left: 0px; width: 1784px;"></div>

                        <script type="text/javascript">

                            $(function () {
                                var resize = function () {
                                    $("#smv_tem_1_45 >.yibuFrameContent>.fullcolumn-inner").width($("#smv_tem_1_45").parent().width());
                                    $('#bannerWrap_tem_1_45').fullScreen(function (t) {
                                        if (VisitFromMobile()) {
                                            t.css("min-width", t.parent().width())
                                        }
                                    });
                                }
                                if (typeof (LayoutConverter) !== "undefined") {
                                    LayoutConverter.CtrlJsVariableList.push({
                                        CtrlId: "tem_1_45",
                                        ResizeFunc: resize,
                                    });
                                } else {
                                    $(window).resize(function (e) {
                                        if (e.target == this) {
                                            resize();
                                        }
                                    });
                                }
                                resize();
                            });
                        </script>
                    </div>
                </div>
                <div id="smv_tem_2_50" ctype="image" class="esmartMargin smartAbs smartFixed  " cpid="60135"
                    cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="" tareaid="Area0"
                    re-direction="all" daxis="All" isdeletable="True"
                    style="height: 50px; width: 187px; left: 50px; top: 10px;z-index:16;">
                    <div class="yibuFrameContent tem_2_50  image_Style1  " style="overflow:visible;;">
                        <div class="w-image-box image-clip-wrap" data-filltype="0" id="div_tem_2_50"
                            style="height: 50px;">
                            <a target="_self" href="index.html">
                                <img loading="lazy" src="images/LOGO.ico" alt="未标题-1" title="" id="img_smv_tem_2_50"
                                    style="width: 50px; height: 50px;" class="">
                                    <label>联创智能</label>
                            </a>
                        </div>
                        <script type="text/javascript">
                            $(function () {
                                InitImageSmv("tem_2_50", "185", "50", "0");
                            });
                        </script>

                    </div>
                </div>
                <div id="smv_tem_28_38" ctype="nav" class="esmartMargin smartAbs smartFixed   " cpid="60135"
                    cstyle="Style7" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="" tareaid="Area0"
                    re-direction="all" daxis="All" isdeletable="True"
                    style="height: 70px; width: 700px; right: 85px; top: 0px;z-index:18;">
                    <div class="yibuFrameContent tem_28_38  nav_Style7  " style="overflow:visible;;">
                        <div id="nav_tem_28_38" class="nav_pc_t_7">
                            <ul class="w-nav" navstyle="style7" style="width:auto;">
                                <li class="w-nav-inner" style="height:70px;line-height:70px;">
                                    <div class="w-nav-item current">
                                        <a href="index.html" target="_self" class="w-nav-item-link">
                                            <span class="w-link-txt">首页</span>
                                        </a>

                                    </div>
                                </li>
                                <li class="w-nav-inner" style="height:70px;line-height:70px;">
                                    <div class="w-nav-item">
                                        <a href="smartAI.html" target="_self" class="w-nav-item-link">
                                            <span class="w-link-txt">AI平台软件</span>
                                        </a>
                                    </div>
                                    <ul class="w-subnav"
                                        style="width: 220px; display: none; height: 400px; padding-top: 0px; margin-top: 0px; padding-bottom: 0px; margin-bottom: 0px;">
                                        <li class="w-subnav-item">
                                            <a href="smartAI_CM.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">采煤工作面智能监察系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_JJ.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">掘进工作面智能监察系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_CNL.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">超能力生产智能分析系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_CDY.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">超定员智能分析系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_JXGJDD.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">井下关键地点视频智能分析系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_DDSKG.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">调度室空岗、睡岗智能分析系统</span>
                                            </a>
                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_YJZH.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">应急指挥通讯系统</span>
                                            </a>
                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_MKXJRY.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">煤矿下井人员、设备精准定位系统</span>
                                            </a>
                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_FZYS.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">辅助运输智能检测系统</span>
                                            </a>
                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_JSYTSF.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">一通三防智能检测系统</span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                                <li class="w-nav-inner" style="height:70px;line-height:70px;">
                                    <div class="w-nav-item">
                                        <a href="kyList.html" target="_self" class="w-nav-item-link">
                                            <span class="w-link-txt">智能硬件</span>
                                        </a>
                                    </div>
                                </li>
                                <li class="w-nav-inner" style="height:70px;line-height:70px;">
                                    <div class="w-nav-item">
                                        <a href="sovleWay.html" target="_self" class="w-nav-item-link">
                                            <span class="w-link-txt">系统解决方案</span>
                                        </a>
                                    </div>
                                    <ul class="w-subnav"
                                        style="width: 190px; display: none; height: 280px; padding-top: 0px; margin-top: 0px; padding-bottom: 0px; margin-bottom: 0px;">
                                        <li class="w-subnav-item">
                                            <a href="sovleWay.html#smv_con_181_4" target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">综合自动化管理平台</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="sovleWay.html#smv_con_182_20" target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">变电所自动化系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="sovleWay.html#smv_con_183_34" target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">运输自动化系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="sovleWay.html#smv_con_184_52" target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">排水自动化系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="sovleWay.html#smv_con_185_5" target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">通风机自动化系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="sovleWay.html#smv_con_186_19" target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">压风自动化系统</span>
                                            </a>
                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="sovleWay.html#smv_con_172_19" target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="w-link-txt">万兆环网集控系统</span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                                <li class="w-nav-inner" style="height:70px;line-height:70px;">
                                    <div class="w-nav-item">
                                        <a href="supportService.html" target="_self" class="w-nav-item-link">
                                            <span class="w-link-txt">支持服务</span>
                                        </a>
                                    </div>
                                </li>
                                <li class="w-nav-inner" style="height:70px;line-height:70px;">
                                    <div class="w-nav-item">
                                        <a href="aboutUs.html" target="_self" class="w-nav-item-link">
                                            <span class="w-link-txt">关于我们</span>
                                        </a>

                                    </div>
                                </li>

                            </ul>
                        </div>
                        <script>
                            $(function () {
                                $('#nav_tem_28_38 .w-nav').find('.w-subnav').hide();
                                var $this, item, itemAll;

                                if ("False".toLocaleLowerCase() == "true") {
                                } else {
                                    //$("#nav_tem_28_38 .w-subnav").css("width", "190" + "px");
                                }

                                $('#nav_tem_28_38 .w-nav').off('mouseenter').on('mouseenter', '.w-nav-inner', function () {
                                    itemAll = $('#nav_tem_28_38 .w-nav').find('.w-subnav');
                                    $this = $(this);
                                    item = $this.find('.w-subnav');
                                    item.slideDown();
                                }).off('mouseleave').on('mouseleave', '.w-nav-inner', function () {
                                    item = $(this).find('.w-subnav');
                                    item.stop().slideUp();
                                });
                                SetNavSelectedStyle('nav_tem_28_38');//选中当前导航
                            });
                        </script>
                    </div>
                </div>
            </div>
        </div>
        <div class="main-layout-wrapper" id="smv_AreaMainWrapper"
            style="background-color: transparent; background-image: none;
             background-repeat: no-repeat;background-position:0 0; background:-moz-linear-gradient(top, none, none);background:-webkit-gradient(linear, left top, left bottom, from(none), to(none));background:-o-linear-gradient(top, none, none);background:-ms-linear-gradient(top, none, none);background:linear-gradient(top, none, none);;background-size: auto;"
            bgscroll="none">
            <div class="main-layout" id="tem-main-layout11" style="width: 100%;">
                <div style="display: none">

                </div>
                <div class="" id="smv_MainContent" rel="mainContentWrapper"
                    style="width: 100%; min-height: 300px; position: relative; ">

                    <div class="smvWrapper"
                        style="min-width:1200px;  position: relative; background-color: transparent; background-image: none; background-repeat: no-repeat; background:-moz-linear-gradient(top, none, none);background:-webkit-gradient(linear, left top, left bottom, from(none), to(none));background:-o-linear-gradient(top, none, none);background:-ms-linear-gradient(top, none, none);background:linear-gradient(top, none, none);;background-position:0 0;background-size:auto;"
                        bgscroll="none">
                        <div class="smvContainer" id="smv_Main" cpid="60478"
                            style="min-height:400px;width:1200px;height:4284px;  position: relative; ">
                            <div id="smv_con_1_26" ctype="slideset" class="esmartMargin smartAbs " cpid="60478" cstyle="Style1" ccolor="Item0" areaid="" iscontainer="True" pvid="" tareaid="" re-direction="y" daxis="Y" isdeletable="True" style="height: 380px; width: 100%; left: 0px; top: 0px;z-index:0;" selectarea="Area72343"><div class="yibuFrameContent con_1_26  slideset_Style1  " style="overflow:visible;;">
                                <!--w-slide-->
                                <div id="lider_smv_con_1_26_wrapper">
                                    <div class="w-slide" id="slider_smv_con_1_26" data-jssor-slider="1" style="left: -292px; width: 1784px; visibility: visible; height: 380px;">
                                        <div style="position: absolute; display: block; top: 0px; left: 0px; width: 1784px; height: 380px;"><div style="position: absolute; display: block; top: 0px; left: 0px; width: 1784px; height: 380px;" data-scale-ratio="1"><div class="w-slide-inner" data-u="slides" style="width: 1784px; z-index: 0; position: absolute; top: 0px; left: 0px;"><div style="top: 0px; left: 0px; width: 1784px; height: 380px; position: absolute; z-index: 0; pointer-events: none;"></div></div><div class="w-slide-inner" data-u="slides" style="width: 1784px; z-index: 0; position: absolute; overflow: hidden; top: 0px; left: 0px;"><div style="top: 0px; left: 0px; width: 1784px; height: 380px; position: absolute; background-color: rgb(0, 0, 0); opacity: 0; z-index: 0;"></div>
                                
                                                <div class="content-box" data-area="Area72343" style="z-index: 1; top: 0px; left: 0px; width: 1784px; height: 380px; position: absolute; overflow: hidden;">
                                                    <div id="smc_Area72343" cid="con_1_26" class="smAreaC slideset_AreaC" style="z-index: 1; width: 1200px; position: relative; margin: 0px auto;">
                                                        <div id="smv_con_187_0" ctype="text" smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;&quot;,&quot;animationName&quot;:&quot;flash&quot;,&quot;infinite&quot;:&quot;1&quot;}" class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1" ccolor="Item0" areaid="Area72343" iscontainer="False" pvid="con_1_26" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 70px; width: 360px; left: 420px; top: 155px; z-index: 3; opacity: 1;" sm-finished="true" smexecuted="1"><div class="yibuFrameContent con_187_0  text_Style1  " style="overflow: hidden; z-index: 1;"><div id="txt_con_187_0" style="height: 100%; z-index: 1;">
                                    <div class="editableContent" id="txtc_con_187_0" style="height: 100%; overflow-wrap: break-word; z-index: 1;">
                                        <p style="text-align: center; z-index: 1;"><span style="font-size: 40px; z-index: 1;"><span style="line-height: 1.5; z-index: 1;"><span style="font-family: SimHei; z-index: 1;"><span style="color: rgb(255, 255, 255); z-index: 1;">解决方案</span></span></span></span></p>
                                
                                    </div>
                                </div>
                                
                                <script style="z-index: 1;">
                                    var tables = $(' #smv_con_187_0').find('table')
                                    for (var i = 0; i < tables.length; i++) {
                                        var tab = tables[i]
                                        var borderWidth = $(tab).attr('border')
                                        if (borderWidth <= 0 || !borderWidth) {
                                            console.log(tab)
                                            $(tab).addClass('hidden-border')
                                            $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                            $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                            $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                            $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                            $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                            $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                        }
                                    }
                                </script></div></div></div>
                                                    <div class="content-box-inner" style="background-image: url(&quot;images/15336758.jpg&quot;); background-position: 50% 50%; background-size: cover; z-index: 1;"></div>
                                
                                                <div style="top: 0px; left: 0px; width: 1784px; height: 380px; z-index: 1000; display: none;"></div></div>
                                        </div></div></div>
                                        <!-- Bullet Navigator -->
                                        <div style="position: absolute; display: block; right: 16px; bottom: 16px; left: 884.5px; width: 15px; height: 15px;"><div data-u="navigator" class="w-slide-btn-box  f-hide " data-autocenter="1" data-scale-ratio="1" style="width: 15px; left: 0px; height: 15px; top: 0px;">
                                            <!-- bullet navigator item prototype -->
                                            
                                        <div class="w-slide-btn w-slide-btnav" data-u="prototype" data-jssor-button="1" style="position: absolute; left: 0px; top: 0px;"></div></div></div>
                                
                                    </div>
                                </div>
                            </div></div>
                            <div id="smv_con_188_51" ctype="area" class="esmartMargin smartAbs " cpid="60478"
                                cstyle="Style1" ccolor="Item0" areaid="" iscontainer="True" pvid="" tareaid=""
                                re-direction="all" daxis="All" isdeletable="True"
                                style="height: 30px; width: 1200px; left: 0px; top: 560px;z-index:0;">
                                <div class="yibuFrameContent con_188_51  area_Style1  " style="overflow:visible;;">
                                    <div class="w-container" data-effect-name="enterTop">
                                        <div class="smAreaC" id="smc_Area0" cid="con_188_51">
                                            <div id="smv_con_189_2" ctype="nav" class="esmartMargin smartAbs "
                                                cpid="60478" cstyle="Style6" ccolor="Item0" areaid="Area0"
                                                iscontainer="False" pvid="con_188_51" tareaid="" re-direction="all"
                                                daxis="All" isdeletable="True"
                                                style="height: 32px; width: 1200px; left: 0px; top: -2px;z-index:1000;">
                                                <div class="yibuFrameContent con_189_2  nav_Style6  "
                                                    style="overflow:visible;;">
                                                    <div id="nav_con_189_2" class="nav_pc_t_6">
                                                        <ul class="w-nav" navstyle="style6">
                                                            <li class="w-nav-inner"
                                                                style="height:32px;line-height:32px;width:15%;">
                                                                <div class="w-nav-item">
                                                                    <a href="#smv_con_181_4" target="_self"
                                                                        class="w-nav-item-link"
                                                                        style="margin-top: 0px;">
                                                                        <span class="mw-iconfont"></span>
                                                                        <span class="w-link-txt">综合自动化管理平台</span>
                                                                    </a>
                                                                    <a href="#smv_con_181_4" target="_self"
                                                                        class="w-nav-item-link hover">
                                                                        <span class="mw-iconfont"></span>
                                                                        <span class="w-link-txt">综合自动化管理平台</span>
                                                                    </a>
                                                                </div>
                                                            </li>
                                                            <li class="w-nav-inner"
                                                                style="height:32px;line-height:32px;width:14%;">
                                                                <div class="w-nav-item">
                                                                    <a href="#smv_con_182_20" target="_self"
                                                                        class="w-nav-item-link"
                                                                        style="margin-top: 0px;">
                                                                        <span class="mw-iconfont"></span>
                                                                        <span class="w-link-txt">变电所自动化系统</span>
                                                                    </a>
                                                                    <a href="#smv_con_182_20" target="_self"
                                                                        class="w-nav-item-link hover">
                                                                        <span class="mw-iconfont"></span>
                                                                        <span class="w-link-txt">变电所自动化系统</span>
                                                                    </a>
                                                                </div>
                                                            </li>
                                                            <li class="w-nav-inner"
                                                                style="height:32px;line-height:32px;width:14%;">
                                                                <div class="w-nav-item">
                                                                    <a href="#smv_con_183_34" target="_self"
                                                                        class="w-nav-item-link"
                                                                        style="margin-top: 0px;">
                                                                        <span class="mw-iconfont"></span>
                                                                        <span class="w-link-txt">运输自动化系统</span>
                                                                    </a>
                                                                    <a href="#smv_con_183_34" target="_self"
                                                                        class="w-nav-item-link hover">
                                                                        <span class="mw-iconfont"></span>
                                                                        <span class="w-link-txt">运输自动化系统</span>
                                                                    </a>
                                                                </div>
                                                            </li>
                                                            <li class="w-nav-inner"
                                                                style="height:32px;line-height:32px;width:14%;">
                                                                <div class="w-nav-item">
                                                                    <a href="#smv_con_184_52" target="_self"
                                                                        class="w-nav-item-link"
                                                                        style="margin-top: 0px;">
                                                                        <span class="mw-iconfont"></span>
                                                                        <span class="w-link-txt">排水自动化系统</span>
                                                                    </a>
                                                                    <a href="#smv_con_184_52" target="_self"
                                                                        class="w-nav-item-link hover">
                                                                        <span class="mw-iconfont"></span>
                                                                        <span class="w-link-txt">排水自动化系统</span>
                                                                    </a>
                                                                </div>
                                                            </li>
                                                            <li class="w-nav-inner"
                                                                style="height:32px;line-height:32px;width:14%;">
                                                                <div class="w-nav-item">
                                                                    <a href="#smv_con_185_5" target="_self"
                                                                        class="w-nav-item-link">
                                                                        <span class="mw-iconfont"></span>
                                                                        <span class="w-link-txt">通风机自动化系统</span>
                                                                    </a>
                                                                    <a href="#smv_con_185_5" target="_self"
                                                                        class="w-nav-item-link hover">
                                                                        <span class="mw-iconfont"></span>
                                                                        <span class="w-link-txt">通风机自动化系统</span>
                                                                    </a>
                                                                </div>
                                                            </li>
                                                            <li class="w-nav-inner"
                                                                style="height:32px;line-height:32px;width:14%;">
                                                                <div class="w-nav-item">
                                                                    <a href="#smv_con_186_19" target="_self"
                                                                        class="w-nav-item-link">
                                                                        <span class="mw-iconfont"></span>
                                                                        <span class="w-link-txt">压风自动化系统</span>
                                                                    </a>
                                                                    <a href="#smv_con_186_19" target="_self"
                                                                        class="w-nav-item-link hover">
                                                                        <span class="mw-iconfont"></span>
                                                                        <span class="w-link-txt">压风自动化系统</span>
                                                                    </a>
                                                                </div>
                                                            </li>
                                                            <li class="w-nav-inner"
                                                                style="height:32px;line-height:32px;width:15%;">
                                                                <div class="w-nav-item">
                                                                    <a href="#smv_con_172_19" target="_self"
                                                                        class="w-nav-item-link">
                                                                        <span class="mw-iconfont"></span>
                                                                        <span class="w-link-txt">万兆环网集控系统</span>
                                                                    </a>
                                                                    <a href="#smv_con_172_19" target="_self"
                                                                        class="w-nav-item-link hover">
                                                                        <span class="mw-iconfont"></span>
                                                                        <span class="w-link-txt">万兆环网集控系统</span>
                                                                    </a>
                                                                </div>
                                                            </li>

                                                        </ul>
                                                    </div>
                                                    <script>
                                                        $(function () {
                                                            var itemHover, $this, item, itemAll, navHei, link;
                                                            $('#nav_con_189_2 .w-nav').find('.w-subnav').hide();
                                                            $('#nav_con_189_2 .w-nav').off('mouseenter').on('mouseenter', '.w-nav-inner', function () {
                                                                navHei = $('#nav_con_189_2 .w-nav').height();
                                                                itemAll = $('#nav_con_189_2 .w-nav').find('.w-subnav');
                                                                $this = $(this);
                                                                link = $this.find('.w-nav-item-link').eq(0);
                                                                item = $this.find('.w-subnav');
                                                                link.stop().animate({ marginTop: -navHei }, 300)
                                                                item.slideDown();
                                                            }).off('mouseleave').on('mouseleave', '.w-nav-inner', function () {
                                                                $this = $(this);
                                                                item = $this.find('.w-subnav');
                                                                link = $this.find('.w-nav-item-link').eq(0);
                                                                link.stop().animate({ marginTop: 0 }, 300);
                                                                item.stop().slideUp();
                                                            });
                                                            SetNavSelectedStyle('nav_con_189_2');//选中当前导航
                                                        });
                                                    </script>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="smv_con_127_22" ctype="area"
                                smanim="{&quot;delay&quot;:0.0,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Down&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1" ccolor="Item1"
                                areaid="Main" iscontainer="True" pvid="" tareaid="Main" re-direction="all" daxis="All"
                                isdeletable="True"
                                style="height: 100px; width: 496px; left: 352px; top: 450px; z-index: 9; opacity: 1;"
                                sm-finished="true" smexecuted="1">
                                <div class="yibuFrameContent con_127_22  area_Style1  " style="overflow:visible;;">
                                    <div class="w-container" data-effect-name="enterTop">
                                        <div class="smAreaC" id="smc_Area0" cid="con_127_22">
                                            <div id="smv_con_128_22" ctype="line" class="esmartMargin smartAbs "
                                                cpid="60478" cstyle="Style1" ccolor="Item0" areaid="Area0"
                                                iscontainer="False" pvid="con_127_22" tareaid="" re-direction="x"
                                                daxis="All" isdeletable="True"
                                                style="height: 20px; width: 279px; left: 108px; top: 75px;z-index:7;">
                                                <div class="yibuFrameContent con_128_22  line_Style1  "
                                                    style="overflow:visible;;">
                                                    <!-- w-line -->
                                                    <div style="position:relative; height:100%">
                                                        <div class="w-line" style="position:absolute;top:50%;"
                                                            linetype="horizontal"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div id="smv_con_129_22" ctype="text" class="esmartMargin smartAbs "
                                                cpid="60478" cstyle="Style1" ccolor="Item5" areaid="Area0"
                                                iscontainer="False" pvid="con_127_22" tareaid="" re-direction="all"
                                                daxis="All" isdeletable="True"
                                                style="height: 21px; width: 360px; left: 68px; top: 50px;z-index:6;">
                                                <div class="yibuFrameContent con_129_22  text_Style1  "
                                                    style="overflow:hidden;;">
                                                    <div id="txt_con_129_22" style="height: 100%;">
                                                        <div class="editableContent" id="txtc_con_129_22"
                                                            style="height: 100%; word-wrap:break-word;">
                                                            <p style="text-align:center"><span
                                                                    style="line-height:1.5"><span
                                                                        style="font-family:Arial,Helvetica,sans-serif"><span
                                                                            style="color:#666666"><span
                                                                                style="font-size:12px">Industry
                                                                                solutions</span></span></span></span>
                                                            </p>

                                                        </div>
                                                    </div>

                                                    <script>
                                                        var tables = $(' #smv_con_129_22').find('table')
                                                        for (var i = 0; i < tables.length; i++) {
                                                            var tab = tables[i]
                                                            var borderWidth = $(tab).attr('border')
                                                            if (borderWidth <= 0 || !borderWidth) {
                                                                console.log(tab)
                                                                $(tab).addClass('hidden-border')
                                                                $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                                $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                                $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                                $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                                $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                                $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                                            }
                                                        }
                                                    </script>
                                                </div>
                                            </div>
                                            <div id="smv_con_130_22" ctype="line" class="esmartMargin smartAbs "
                                                cpid="60478" cstyle="Style1" ccolor="Item3" areaid="Area0"
                                                iscontainer="False" pvid="con_127_22" tareaid="" re-direction="x"
                                                daxis="All" isdeletable="True"
                                                style="height: 20px; width: 70px; left: 213px; top: 73px;z-index:8;">
                                                <div class="yibuFrameContent con_130_22  line_Style1  "
                                                    style="overflow:visible;;">
                                                    <!-- w-line -->
                                                    <div style="position:relative; height:100%">
                                                        <div class="w-line" style="position:absolute;top:50%;"
                                                            linetype="horizontal"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div id="smv_con_131_22" ctype="text" class="esmartMargin smartAbs "
                                                cpid="60478" cstyle="Style1" ccolor="Item0" areaid="Area0"
                                                iscontainer="False" pvid="con_127_22" tareaid="" re-direction="all"
                                                daxis="All" isdeletable="True"
                                                style="height: 34px; width: 360px; left: 68px; top: 9px;z-index:2;">
                                                <div class="yibuFrameContent con_131_22  text_Style1  "
                                                    style="overflow:hidden;;">
                                                    <div id="txt_con_131_22" style="height: 100%;">
                                                        <div class="editableContent" id="txtc_con_131_22"
                                                            style="height: 100%; word-wrap:break-word;">
                                                            <p style="text-align:center"><span
                                                                    style="line-height:1.2"><span
                                                                        style="font-size:28px"><span
                                                                            style="font-family:Arial,Helvetica,sans-serif"><span
                                                                                style="color:#000000">解决方案</span></span></span></span>
                                                            </p>

                                                        </div>
                                                    </div>

                                                    <script>
                                                        var tables = $(' #smv_con_131_22').find('table')
                                                        for (var i = 0; i < tables.length; i++) {
                                                            var tab = tables[i]
                                                            var borderWidth = $(tab).attr('border')
                                                            if (borderWidth <= 0 || !borderWidth) {
                                                                console.log(tab)
                                                                $(tab).addClass('hidden-border')
                                                                $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                                $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                                $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                                $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                                $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                                $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                                            }
                                                        }
                                                    </script>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="smv_con_138_47" ctype="banner" class="esmartMargin smartAbs " cpid="60478"
                                cstyle="Style1" ccolor="Item0" areaid="" iscontainer="True" pvid="" tareaid=""
                                re-direction="y" daxis="Y" isdeletable="True"
                                style="height: 500px; width: 100%; left: 0px; top: 593px;z-index:0;">
                                <div class="yibuFrameContent con_138_47  banner_Style1  " style="overflow:visible;;">
                                    <div class="fullcolumn-inner smAreaC" id="smc_Area0" cid="con_138_47"
                                        style="width:1200px">
                                        <div id="smv_con_139_54" ctype="image"
                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Left&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                            class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1"
                                            ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_138_47"
                                            tareaid="" re-direction="all" daxis="All" isdeletable="True"
                                            style="height: 400px; width: 580px; left: 20px; top: 50px; z-index: 2; opacity: 1;"
                                            sm-finished="true" smexecuted="1">
                                            <div class="yibuFrameContent con_139_54  image_Style1  "
                                                style="overflow:visible;;">
                                                <div class="w-image-box image-clip-wrap" data-filltype="1"
                                                    id="div_con_139_54" style="height: 400px;">
                                                    <a target="_self">
                                                        <img loading="lazy" src="images/zhzdhptjjfa.png" alt="12893737"
                                                            title="" id="img_smv_con_139_54"
                                                            
                                                            class="">
                                                    </a>
                                                </div>

                                                <script type="text/javascript">
                                                    $(function () {

                                                        //InitImageSmv("con_139_54", "580", "600", "1");

                                                    });
                                                </script>

                                            </div>
                                        </div>
                                        <div id="smv_con_159_34" ctype="area"
                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Right&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                            class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1"
                                            ccolor="Item0" areaid="Area0" iscontainer="True" pvid="con_138_47"
                                            tareaid="" re-direction="all" daxis="All" isdeletable="True"
                                            style="height: 338px; width: 560px; left: 620px; top: 81px; z-index: 1; opacity: 1;"
                                            sm-finished="true" smexecuted="1">
                                            <div class="yibuFrameContent con_159_34  area_Style1  "
                                                style="overflow:visible;;">
                                                <div class="w-container" data-effect-name="enterTop">
                                                    <div class="smAreaC" id="smc_Area0" cid="con_159_34">
                                                        <div id="smv_con_140_28" ctype="text"
                                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Right&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                                            class="esmartMargin smartAbs animated" cpid="60478"
                                                            cstyle="Style1" ccolor="Item0" areaid="Area0"
                                                            iscontainer="False" pvid="con_159_34" tareaid=""
                                                            re-direction="all" daxis="All" isdeletable="True"
                                                            style="height: 260px; width: 520px; left: 19px; top: 64px; z-index: 3; opacity: 1;"
                                                            sm-finished="true" smexecuted="1">
                                                            <div class="yibuFrameContent con_140_28  text_Style1  "
                                                                style="overflow:hidden;;">
                                                                <div id="txt_con_140_28" style="height: 100%;">
                                                                    <div class="editableContent" id="txtc_con_140_28"
                                                                        style="height: 100%; word-wrap:break-word;">
                                                                        <p style="text-align:justify"><span
                                                                                style="color:#004ea2"><strong><span
                                                                                        style="line-height:1.75"><span
                                                                                            style="font-size:22px">综合自动化管理平台</span></span></strong></span>
                                                                        </p>

                                                                        <p style="text-align:justify">&nbsp;</p>

                                                                        <p style="text-align:justify">&nbsp;</p>

                                                                        <p style="text-align:justify"><span
                                                                                style="line-height:1.75"><span
                                                                                    style="font-size:16px">在统一的网络平台上，实现矿山生产数据的精准采集、网络化传输和规范化集成；建立统一的集成控制平台，实现生产全过程一体化控制；建设基于大数据中心的生产信息数据服务系统；实现生产过程无人（少人），实现监控一体化；将实现原煤运输、供电、排水、通风、压风等环节远程全面监控和无人值守。以上计划推进后，后续可持续深入推进其它各子系统的信息融合，最终将煤矿建设成为安全、高效、智能、绿色的自动化煤矿。</span></span>
                                                                        </p>

                                                                    </div>
                                                                </div>

                                                                <script>
                                                                    var tables = $(' #smv_con_140_28').find('table')
                                                                    for (var i = 0; i < tables.length; i++) {
                                                                        var tab = tables[i]
                                                                        var borderWidth = $(tab).attr('border')
                                                                        if (borderWidth <= 0 || !borderWidth) {
                                                                            console.log(tab)
                                                                            $(tab).addClass('hidden-border')
                                                                            $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                                            $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                                            $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                                                        }
                                                                    }
                                                                </script>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="smv_con_182_20" ctype="text" class="esmartMargin smartAbs "
                                            cpid="60478" cstyle="Style1" ccolor="Item0" areaid="Area0"
                                            iscontainer="False" pvid="con_138_47" tareaid="" re-direction="all"
                                            daxis="All" isdeletable="True"
                                            style="height: 35px; width: 255px; left: 6px; top: 460px;z-index:4;">
                                            <div class="yibuFrameContent con_182_20  text_Style1  "
                                                style="overflow:hidden;;">
                                                <div id="txt_con_182_20" style="height: 100%;">
                                                    <div class="editableContent" id="txtc_con_182_20"
                                                        style="height: 100%; word-wrap:break-word;">
                                                        <p><a id="2" name="2"></a></p>

                                                    </div>
                                                </div>

                                                <script>
                                                    var tables = $(' #smv_con_182_20').find('table')
                                                    for (var i = 0; i < tables.length; i++) {
                                                        var tab = tables[i]
                                                        var borderWidth = $(tab).attr('border')
                                                        if (borderWidth <= 0 || !borderWidth) {
                                                            console.log(tab)
                                                            $(tab).addClass('hidden-border')
                                                            $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                            $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                            $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                                        }
                                                    }
                                                </script>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="bannerWrap_con_138_47" class="fullcolumn-outer"
                                        style="position: absolute; top: 0px; bottom: 0px; left: -292px; width: 1784px;">
                                    </div>

                                    <script type="text/javascript">

                                        $(function () {
                                            var resize = function () {
                                                $("#smv_con_138_47 >.yibuFrameContent>.fullcolumn-inner").width($("#smv_con_138_47").parent().width());
                                                $('#bannerWrap_con_138_47').fullScreen(function (t) {
                                                    if (VisitFromMobile()) {
                                                        t.css("min-width", t.parent().width())
                                                    }
                                                });
                                            }
                                            if (typeof (LayoutConverter) !== "undefined") {
                                                LayoutConverter.CtrlJsVariableList.push({
                                                    CtrlId: "con_138_47",
                                                    ResizeFunc: resize,
                                                });
                                            } else {
                                                $(window).resize(function (e) {
                                                    if (e.target == this) {
                                                        resize();
                                                    }
                                                });
                                            }

                                            resize();
                                        });
                                    </script>
                                </div>
                            </div>
                            <div id="smv_con_160_0" ctype="banner" class="esmartMargin smartAbs " cpid="60478"
                                cstyle="Style1" ccolor="Item0" areaid="Main" iscontainer="True" pvid="" tareaid="Main"
                                re-direction="y" daxis="Y" isdeletable="True"
                                style="height: 500px; width: 100%; left: 0px; top: 1188px;z-index:0;">
                                <div class="yibuFrameContent con_160_0  banner_Style1  " style="overflow:visible;;">
                                    <div class="fullcolumn-inner smAreaC" id="smc_Area0" cid="con_160_0"
                                        style="width:1200px">
                                        <div id="smv_con_162_0" ctype="area"
                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Left&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                            class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1"
                                            ccolor="Item0" areaid="Area0" iscontainer="True" pvid="con_160_0" tareaid=""
                                            re-direction="all" daxis="All" isdeletable="True"
                                            style="height: 338px; width: 560px; left: 20px; top: 81px; z-index: 1; opacity: 1;"
                                            sm-finished="true" smexecuted="1">
                                            <div class="yibuFrameContent con_162_0  area_Style1  "
                                                style="overflow:visible;;">
                                                <div class="w-container" data-effect-name="enterTop">
                                                    <div class="smAreaC" id="smc_Area0" cid="con_162_0">
                                                        <div id="smv_con_163_0" ctype="text"
                                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Left&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                                            class="esmartMargin smartAbs animated" cpid="60478"
                                                            cstyle="Style1" ccolor="Item0" areaid="Area0"
                                                            iscontainer="False" pvid="con_162_0" tareaid=""
                                                            re-direction="all" daxis="All" isdeletable="True"
                                                            style="height: 260px; width: 520px; left: 19px; top: 64px; z-index: 3; opacity: 1;"
                                                            sm-finished="true" smexecuted="1">
                                                            <div class="yibuFrameContent con_163_0  text_Style1  "
                                                                style="overflow:hidden;;">
                                                                <div id="txt_con_163_0" style="height: 100%;">
                                                                    <div class="editableContent" id="txtc_con_163_0"
                                                                        style="height: 100%; word-wrap:break-word;">
                                                                        <p style="text-align:justify"><span
                                                                                style="font-size:22px"><strong><span
                                                                                        style="color:#004ea2"><span
                                                                                            style="line-height:1.75">变电所自动化系统</span></span></strong></span>
                                                                        </p>

                                                                        <p style="text-align:justify">&nbsp;</p>

                                                                        <p style="text-align:justify">&nbsp;</p>

                                                                        <p style="text-align:justify"><span
                                                                            style="line-height:1.75"><span
                                                                                style="font-size:16px">煤矿井下变电所远程监控系统，是根据我国煤炭企业生产供电的特点和管理模式精心设计的以计算机数字通讯技术为基础的远程分布式监测、控制系统。适用于煤矿供电系统的远程测控，实现地面调度中心对井下供电设备的遥测、遥调和遥控，并可生成多种记录和统计报表，以达到减人增效的目的。本系统支持地面办公局域网和矿井工业以太网，可使煤矿井下高压、低压供电管理实现无人值守，调高煤矿供电智能化调度和信息化管理水平。</span></span>
                                                                        </p>

                                                                       

                                                                    </div>
                                                                </div>

                                                                <script>
                                                                    var tables = $(' #smv_con_163_0').find('table')
                                                                    for (var i = 0; i < tables.length; i++) {
                                                                        var tab = tables[i]
                                                                        var borderWidth = $(tab).attr('border')
                                                                        if (borderWidth <= 0 || !borderWidth) {
                                                                            console.log(tab)
                                                                            $(tab).addClass('hidden-border')
                                                                            $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                                            $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                                            $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                                                        }
                                                                    }
                                                                </script>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="smv_con_161_0" ctype="image"
                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Right&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                            class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1"
                                            ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_160_0"
                                            tareaid="" re-direction="all" daxis="All" isdeletable="True"
                                            style="height: 400px; width: 580px; left: 600px; top: 50px; z-index: 2; opacity: 1;"
                                            sm-finished="true" smexecuted="1">
                                            <div class="yibuFrameContent con_161_0  image_Style1  "
                                                style="overflow:visible;;">
                                                <div class="w-image-box image-clip-wrap" data-filltype="1"
                                                    id="div_con_161_0" style="height: 400px;">
                                                    <a target="_self">
                                                        <img loading="lazy" src="images/bdszdhxt.png" alt="12024709"
                                                            title="" id="img_smv_con_161_0"
                                                            style="width: auto; height: 400px; margin-left: -53.1053px; margin-top: 0px;"
                                                            class="">
                                                    </a>
                                                </div>

                                                <script type="text/javascript">
                                                    $(function () {

                                                        InitImageSmv("con_161_0", "578", "400", "1");

                                                    });
                                                </script>

                                            </div>
                                        </div>
                                        <div id="smv_con_183_34" ctype="text" class="esmartMargin smartAbs "
                                            cpid="60478" cstyle="Style1" ccolor="Item0" areaid="Area0"
                                            iscontainer="False" pvid="con_160_0" tareaid="Main" re-direction="all"
                                            daxis="All" isdeletable="True"
                                            style="height: 30px; width: 360px; left: 9px; top: 444px;z-index:4;">
                                            <div class="yibuFrameContent con_183_34  text_Style1  "
                                                style="overflow:hidden;;">
                                                <div id="txt_con_183_34" style="height: 100%;">
                                                    <div class="editableContent" id="txtc_con_183_34"
                                                        style="height: 100%; word-wrap:break-word;">
                                                        <p><a id="3" name="3"></a></p>

                                                    </div>
                                                </div>

                                                <script>
                                                    var tables = $(' #smv_con_183_34').find('table')
                                                    for (var i = 0; i < tables.length; i++) {
                                                        var tab = tables[i]
                                                        var borderWidth = $(tab).attr('border')
                                                        if (borderWidth <= 0 || !borderWidth) {
                                                            console.log(tab)
                                                            $(tab).addClass('hidden-border')
                                                            $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                            $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                            $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                                        }
                                                    }
                                                </script>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="bannerWrap_con_160_0" class="fullcolumn-outer"
                                        style="position: absolute; top: 0px; bottom: 0px; left: -292px; width: 1784px;">
                                    </div>

                                    <script type="text/javascript">

                                        $(function () {
                                            var resize = function () {
                                                $("#smv_con_160_0 >.yibuFrameContent>.fullcolumn-inner").width($("#smv_con_160_0").parent().width());
                                                $('#bannerWrap_con_160_0').fullScreen(function (t) {
                                                    if (VisitFromMobile()) {
                                                        t.css("min-width", t.parent().width())
                                                    }
                                                });
                                            }
                                            if (typeof (LayoutConverter) !== "undefined") {
                                                LayoutConverter.CtrlJsVariableList.push({
                                                    CtrlId: "con_160_0",
                                                    ResizeFunc: resize,
                                                });
                                            } else {
                                                $(window).resize(function (e) {
                                                    if (e.target == this) {
                                                        resize();
                                                    }
                                                });
                                            }

                                            resize();
                                        });
                                    </script>
                                </div>
                            </div>
                            <div id="smv_con_172_10" ctype="banner" class="esmartMargin smartAbs " cpid="60478"
                                cstyle="Style1" ccolor="Item0" areaid="Main" iscontainer="True" pvid="" tareaid="Main"
                                re-direction="y" daxis="Y" isdeletable="True"
                                style="height: 500px; width: 100%; left: 0px; top: 2685px;z-index:0;">
                                <div class="yibuFrameContent con_172_10  banner_Style1  " style="overflow:visible;;">
                                    <div class="fullcolumn-inner smAreaC" id="smc_Area0" cid="con_172_10"
                                        style="width:1200px">
                                        <div id="smv_con_174_10" ctype="area"
                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Right&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                            class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1"
                                            ccolor="Item0" areaid="Area0" iscontainer="True" pvid="con_172_10"
                                            tareaid="" re-direction="all" daxis="All" isdeletable="True"
                                            style="height: 338px; width: 560px; left: 620px; top: 81px; z-index: 1; opacity: 1;"
                                            sm-finished="true" smexecuted="1">
                                            <div class="yibuFrameContent con_174_10  area_Style1  "
                                                style="overflow:visible;;">
                                                <div class="w-container" data-effect-name="enterTop">
                                                    <div class="smAreaC" id="smc_Area0" cid="con_174_10">
                                                        <div id="smv_con_175_10" ctype="text"
                                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Right&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                                            class="esmartMargin smartAbs animated" cpid="60478"
                                                            cstyle="Style1" ccolor="Item0" areaid="Area0"
                                                            iscontainer="False" pvid="con_174_10" tareaid=""
                                                            re-direction="all" daxis="All" isdeletable="True"
                                                            style="height: 228px; width: 520px; left: 19px; top: 64px; z-index: 3; opacity: 1;"
                                                            sm-finished="true" smexecuted="1">
                                                            <div class="yibuFrameContent con_175_10  text_Style1  "
                                                                style="overflow:hidden;;">
                                                                <div id="txt_con_175_10" style="height: 100%;">
                                                                    <div class="editableContent" id="txtc_con_175_10"
                                                                        style="height: 100%; word-wrap:break-word;">
                                                                        <p style="text-align:justify"><span
                                                                                style="color:#004f9b"><strong><span
                                                                                        style="line-height:1.75"><span
                                                                                            style="font-size:22px">通风机自动化系统</span></span></strong></span>
                                                                        </p>

                                                                        <p style="text-align:justify">&nbsp;</p>

                                                                        <p style="text-align:justify">&nbsp;</p>

                                                                        <p style="text-align:justify"><span
                                                                            style="line-height:1.75"><span
                                                                                style="font-size:16px">矿井主通风机是向井下送风的重要设备，也是大型耗能设备，对其实现在线监控，使之始终运行在良好状态，对于保障煤矿安全生产，保护矿工生命和企业财产安全，降低风机能耗具有重要的意义。</span></span>
                                                                        </p>

                                                                    </div>
                                                                </div>

                                                                <script>
                                                                    var tables = $(' #smv_con_175_10').find('table')
                                                                    for (var i = 0; i < tables.length; i++) {
                                                                        var tab = tables[i]
                                                                        var borderWidth = $(tab).attr('border')
                                                                        if (borderWidth <= 0 || !borderWidth) {
                                                                            console.log(tab)
                                                                            $(tab).addClass('hidden-border')
                                                                            $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                                            $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                                            $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                                                        }
                                                                    }
                                                                </script>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="smv_con_173_10" ctype="image"
                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Left&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                            class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1"
                                            ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_172_10"
                                            tareaid="" re-direction="all" daxis="All" isdeletable="True"
                                            style="height: 400px; width: 580px; left: 20px; top: 50px; z-index: 2; opacity: 1;"
                                            sm-finished="true" smexecuted="1">
                                            <div class="yibuFrameContent con_173_10  image_Style1  "
                                                style="overflow:visible;;">
                                                <div class="w-image-box image-clip-wrap" data-filltype="1"
                                                    id="div_con_173_10" style="height: 400px;">
                                                    <a target="_self">
                                                        <img loading="lazy" src="images/tfjzdhxt.png"
                                                            alt="fd770139-5ebf-41c0-a431-74ef667b08d8" title=""
                                                            id="img_smv_con_173_10"
                                                            
                                                            class="">
                                                    </a>
                                                </div>

                                                <script type="text/javascript">
                                                    $(function () {

                                                        //InitImageSmv("con_173_10", "578", "400", "1");

                                                    });
                                                </script>

                                            </div>
                                        </div>
                                        <div id="smv_con_186_19" ctype="text" class="esmartMargin smartAbs "
                                            cpid="60478" cstyle="Style1" ccolor="Item0" areaid="Area0"
                                            iscontainer="False" pvid="con_172_10" tareaid="Main" re-direction="all"
                                            daxis="All" isdeletable="True"
                                            style="height: 30px; width: 360px; left: 14px; top: 459px;z-index:4;">
                                            <div class="yibuFrameContent con_186_19  text_Style1  "
                                                style="overflow:hidden;;">
                                                <div id="txt_con_186_19" style="height: 100%;">
                                                    <div class="editableContent" id="txtc_con_186_19"
                                                        style="height: 100%; word-wrap:break-word;">
                                                        <p><a id="6" name="6"></a></p>

                                                    </div>
                                                </div>

                                                <script>
                                                    var tables = $(' #smv_con_186_19').find('table')
                                                    for (var i = 0; i < tables.length; i++) {
                                                        var tab = tables[i]
                                                        var borderWidth = $(tab).attr('border')
                                                        if (borderWidth <= 0 || !borderWidth) {
                                                            console.log(tab)
                                                            $(tab).addClass('hidden-border')
                                                            $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                            $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                            $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                                        }
                                                    }
                                                </script>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="bannerWrap_con_172_10" class="fullcolumn-outer"
                                        style="position: absolute; top: 0px; bottom: 0px; left: -292px; width: 1784px;">
                                    </div>

                                    <script type="text/javascript">

                                        $(function () {
                                            var resize = function () {
                                                $("#smv_con_172_10 >.yibuFrameContent>.fullcolumn-inner").width($("#smv_con_172_10").parent().width());
                                                $('#bannerWrap_con_172_10').fullScreen(function (t) {
                                                    if (VisitFromMobile()) {
                                                        t.css("min-width", t.parent().width())
                                                    }
                                                });
                                            }
                                            if (typeof (LayoutConverter) !== "undefined") {
                                                LayoutConverter.CtrlJsVariableList.push({
                                                    CtrlId: "con_172_10",
                                                    ResizeFunc: resize,
                                                });
                                            } else {
                                                $(window).resize(function (e) {
                                                    if (e.target == this) {
                                                        resize();
                                                    }
                                                });
                                            }

                                            resize();
                                        });
                                    </script>
                                </div>
                            </div>
                            <div id="smv_con_168_58" ctype="banner" class="esmartMargin smartAbs " cpid="60478"
                                cstyle="Style1" ccolor="Item0" areaid="Main" iscontainer="True" pvid="" tareaid="Main"
                                re-direction="y" daxis="Y" isdeletable="True"
                                style="height: 500px; width: 100%; left: 0px; top: 2186px;z-index:0;">
                                <div class="yibuFrameContent con_168_58  banner_Style1  " style="overflow:visible;;">
                                    <div class="fullcolumn-inner smAreaC" id="smc_Area0" cid="con_168_58"
                                        style="width:1200px">
                                        <div id="smv_con_170_58" ctype="area"
                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Left&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                            class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1"
                                            ccolor="Item0" areaid="Area0" iscontainer="True" pvid="con_168_58"
                                            tareaid="" re-direction="all" daxis="All" isdeletable="True"
                                            style="height: 338px; width: 560px; left: 20px; top: 81px; z-index: 1; opacity: 1;"
                                            sm-finished="true" smexecuted="1">
                                            <div class="yibuFrameContent con_170_58  area_Style1  "
                                                style="overflow:visible;;">
                                                <div class="w-container" data-effect-name="enterTop">
                                                    <div class="smAreaC" id="smc_Area0" cid="con_170_58">
                                                        <div id="smv_con_171_58" ctype="text"
                                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Left&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                                            class="esmartMargin smartAbs animated" cpid="60478"
                                                            cstyle="Style1" ccolor="Item0" areaid="Area0"
                                                            iscontainer="False" pvid="con_170_58" tareaid=""
                                                            re-direction="all" daxis="All" isdeletable="True"
                                                            style="height: 194px; width: 505px; left: 34px; top: 64px; z-index: 3; opacity: 1;"
                                                            sm-finished="true" smexecuted="1">
                                                            <div class="yibuFrameContent con_171_58  text_Style1  "
                                                                style="overflow:hidden;;">
                                                                <div id="txt_con_171_58" style="height: 100%;">
                                                                    <div class="editableContent" id="txtc_con_171_58"
                                                                        style="height: 100%; word-wrap:break-word;">
                                                                        <p style="text-align:justify"><span
                                                                                style="color:#004f9b"><strong><span
                                                                                        style="line-height:1.75"><span
                                                                                            style="font-size:22px">排水自动化系统</span></span></strong></span>
                                                                        </p>

                                                                        <p style="text-align:justify">&nbsp;</p>

                                                                        <p style="text-align:justify">&nbsp;</p>

                                                                        <p style="text-align:justify"><span
                                                                            style="line-height:1.75"><span
                                                                                style="font-size:16px">煤矿井下水泵是煤矿生产的主要设备之一，实现井下泵房的远程控制于监测，是综合自动化建设的重要组成部分。系统自动排水系统可以用计算机自动检测水仓水位并自动控制某一水泵的开停，检测运行参数、分析差数进行对设备的保护。</span></span>
                                                                        </p>

                                                                    </div>
                                                                </div>

                                                                <script>
                                                                    var tables = $(' #smv_con_171_58').find('table')
                                                                    for (var i = 0; i < tables.length; i++) {
                                                                        var tab = tables[i]
                                                                        var borderWidth = $(tab).attr('border')
                                                                        if (borderWidth <= 0 || !borderWidth) {
                                                                            console.log(tab)
                                                                            $(tab).addClass('hidden-border')
                                                                            $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                                            $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                                            $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                                                        }
                                                                    }
                                                                </script>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="smv_con_169_58" ctype="image"
                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Right&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                            class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1"
                                            ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_168_58"
                                            tareaid="" re-direction="all" daxis="All" isdeletable="True"
                                            style="height: 400px; width: 580px; left: 595px; top: 50px; z-index: 2; opacity: 1;"
                                            sm-finished="true" smexecuted="1">
                                            <div class="yibuFrameContent con_169_58  image_Style1  "
                                                style="overflow:visible;border: 1px solid #cccccc;">
                                                <div class="w-image-box image-clip-wrap" data-filltype="1"
                                                    id="div_con_169_58" style="height: 346px;">
                                                    <a target="_self">
                                                        <img loading="lazy" src="images/pszdhxt.png" alt="1591098085-1"
                                                            title="" id="img_smv_con_169_58"
                                                            
                                                            class="">
                                                    </a>
                                                </div>

                                                <script type="text/javascript">
                                                    $(function () {

                                                        //InitImageSmv("con_169_58", "586", "346", "1");

                                                    });
                                                </script>

                                            </div>
                                        </div>
                                        <div id="smv_con_185_5" ctype="text" class="esmartMargin smartAbs " cpid="60478"
                                            cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False"
                                            pvid="con_168_58" tareaid="Main" re-direction="all" daxis="All"
                                            isdeletable="True"
                                            style="height: 46px; width: 296px; left: 6px; top: 440px;z-index:4;">
                                            <div class="yibuFrameContent con_185_5  text_Style1  "
                                                style="overflow:hidden;;">
                                                <div id="txt_con_185_5" style="height: 100%;">
                                                    <div class="editableContent" id="txtc_con_185_5"
                                                        style="height: 100%; word-wrap:break-word;">
                                                        <p><a id="5" name="5"></a></p>

                                                    </div>
                                                </div>

                                                <script>
                                                    var tables = $(' #smv_con_185_5').find('table')
                                                    for (var i = 0; i < tables.length; i++) {
                                                        var tab = tables[i]
                                                        var borderWidth = $(tab).attr('border')
                                                        if (borderWidth <= 0 || !borderWidth) {
                                                            console.log(tab)
                                                            $(tab).addClass('hidden-border')
                                                            $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                            $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                            $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                                        }
                                                    }
                                                </script>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="bannerWrap_con_168_58" class="fullcolumn-outer"
                                        style="position: absolute; top: 0px; bottom: 0px; left: -292px; width: 1784px;">
                                    </div>

                                    <script type="text/javascript">

                                        $(function () {
                                            var resize = function () {
                                                $("#smv_con_168_58 >.yibuFrameContent>.fullcolumn-inner").width($("#smv_con_168_58").parent().width());
                                                $('#bannerWrap_con_168_58').fullScreen(function (t) {
                                                    if (VisitFromMobile()) {
                                                        t.css("min-width", t.parent().width())
                                                    }
                                                });
                                            }
                                            if (typeof (LayoutConverter) !== "undefined") {
                                                LayoutConverter.CtrlJsVariableList.push({
                                                    CtrlId: "con_168_58",
                                                    ResizeFunc: resize,
                                                });
                                            } else {
                                                $(window).resize(function (e) {
                                                    if (e.target == this) {
                                                        resize();
                                                    }
                                                });
                                            }

                                            resize();
                                        });
                                    </script>
                                </div>
                            </div>
                            <div id="smv_con_164_47" ctype="banner" class="esmartMargin smartAbs " cpid="60478"
                                cstyle="Style1" ccolor="Item0" areaid="Main" iscontainer="True" pvid="" tareaid="Main"
                                re-direction="y" daxis="Y" isdeletable="True"
                                style="height: 500px; width: 100%; left: 0px; top: 1687px;z-index:0;">
                                <div class="yibuFrameContent con_164_47  banner_Style1  " style="overflow:visible;;">
                                    <div class="fullcolumn-inner smAreaC" id="smc_Area0" cid="con_164_47"
                                        style="width:1200px">
                                        <div id="smv_con_165_47" ctype="image"
                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Left&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                            class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1"
                                            ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_164_47"
                                            tareaid="" re-direction="all" daxis="All" isdeletable="True"
                                            style="height: 400px; width: 580px; left: 20px; top: 50px; z-index: 2; opacity: 1;"
                                            sm-finished="true" smexecuted="1">
                                            <div class="yibuFrameContent con_165_47  image_Style1  "
                                                style="overflow:visible;;">
                                                <div class="w-image-box image-clip-wrap" data-filltype="1"
                                                    id="div_con_165_47" style="height: 400px;">
                                                    <a target="_self">
                                                        <img loading="lazy" src="images/yszdhxt.png"
                                                            alt="high-angle-view-of-a-man-256381" title=""
                                                            id="img_smv_con_165_47"
                                                            style=" margin-left: 0px;"
                                                            class="">
                                                    </a>
                                                </div>

                                                <script type="text/javascript">
                                                    $(function () {

                                                        //InitImageSmv("con_165_47", "578", "400", "1");

                                                    });
                                                </script>

                                            </div>
                                        </div>
                                        <div id="smv_con_166_47" ctype="area"
                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Right&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                            class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1"
                                            ccolor="Item0" areaid="Area0" iscontainer="True" pvid="con_164_47"
                                            tareaid="" re-direction="all" daxis="All" isdeletable="True"
                                            style="height: 338px; width: 560px; left: 620px; top: 81px; z-index: 1; opacity: 1;"
                                            sm-finished="true" smexecuted="1">
                                            <div class="yibuFrameContent con_166_47  area_Style1  "
                                                style="overflow:visible;;">
                                                <div class="w-container" data-effect-name="enterTop">
                                                    <div class="smAreaC" id="smc_Area0" cid="con_166_47">
                                                        <div id="smv_con_167_47" ctype="text"
                                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Right&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                                            class="esmartMargin smartAbs animated" cpid="60478"
                                                            cstyle="Style1" ccolor="Item0" areaid="Area0"
                                                            iscontainer="False" pvid="con_166_47" tareaid=""
                                                            re-direction="all" daxis="All" isdeletable="True"
                                                            style="height: 320px; width: 520px; left: 21px; top: 64px; z-index: 3; opacity: 1;"
                                                            sm-finished="true" smexecuted="1">
                                                            <div class="yibuFrameContent con_167_47  text_Style1  "
                                                                style="overflow:hidden;;">
                                                                <div id="txt_con_167_47" style="height: 100%;">
                                                                    <div class="editableContent" id="txtc_con_167_47"
                                                                        style="height: 100%; word-wrap:break-word;">
                                                                        <p style="text-align:justify"><strong><span
                                                                                    style="color:#004ea2"><span
                                                                                        style="line-height:1.75"><span
                                                                                            style="font-size:22px">运输自动化系统</span></span></span></strong>
                                                                        </p>

                                                                        <p style="text-align:justify">&nbsp;</p>

                                                                        <p style="text-align:justify">&nbsp;</p>

                                                                        <p style="text-align:justify"><span
                                                                            style="line-height:1.75"><span
                                                                                style="font-size:16px">胶带输送机已成为煤矿原煤运输生产中非常重要的运输设备，它能否安全高效地运行，直接决定着矿井机电设备的开机率和产量，而老式的胶带运输控制方式，采用人工信号，每条皮带按照信号独立控制开停，系统系统分散，控制的灵活性差，且各皮带的配置差异较大，同时用人工操作，操作人员劳动强度大，运行效率低，且易引起操作失误，造成设备损坏，甚至人员伤亡，给煤矿带来重大的损失。为此实现胶带输送机的集中控制就显得更加必要。</span></span>
                                                                        </p>

                                                                    </div>
                                                                </div>

                                                                <script>
                                                                    var tables = $(' #smv_con_167_47').find('table')
                                                                    for (var i = 0; i < tables.length; i++) {
                                                                        var tab = tables[i]
                                                                        var borderWidth = $(tab).attr('border')
                                                                        if (borderWidth <= 0 || !borderWidth) {
                                                                            console.log(tab)
                                                                            $(tab).addClass('hidden-border')
                                                                            $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                                            $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                                            $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                                                        }
                                                                    }
                                                                </script>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="smv_con_184_52" ctype="text" class="esmartMargin smartAbs "
                                            cpid="60478" cstyle="Style1" ccolor="Item0" areaid="Area0"
                                            iscontainer="False" pvid="con_164_47" tareaid="Main" re-direction="all"
                                            daxis="All" isdeletable="True"
                                            style="height: 36px; width: 292px; left: 15px; top: 459px;z-index:4;">
                                            <div class="yibuFrameContent con_184_52  text_Style1  "
                                                style="overflow:hidden;;">
                                                <div id="txt_con_184_52" style="height: 100%;">
                                                    <div class="editableContent" id="txtc_con_184_52"
                                                        style="height: 100%; word-wrap:break-word;">
                                                        <p><a id="4" name="4"></a></p>

                                                    </div>
                                                </div>

                                                <script>
                                                    var tables = $(' #smv_con_184_52').find('table')
                                                    for (var i = 0; i < tables.length; i++) {
                                                        var tab = tables[i]
                                                        var borderWidth = $(tab).attr('border')
                                                        if (borderWidth <= 0 || !borderWidth) {
                                                            console.log(tab)
                                                            $(tab).addClass('hidden-border')
                                                            $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                            $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                            $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                                        }
                                                    }
                                                </script>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="bannerWrap_con_164_47" class="fullcolumn-outer"
                                        style="position: absolute; top: 0px; bottom: 0px; left: -292px; width: 1784px;">
                                    </div>

                                    <script type="text/javascript">

                                        $(function () {
                                            var resize = function () {
                                                $("#smv_con_164_47 >.yibuFrameContent>.fullcolumn-inner").width($("#smv_con_164_47").parent().width());
                                                $('#bannerWrap_con_164_47').fullScreen(function (t) {
                                                    if (VisitFromMobile()) {
                                                        t.css("min-width", t.parent().width())
                                                    }
                                                });
                                            }
                                            if (typeof (LayoutConverter) !== "undefined") {
                                                LayoutConverter.CtrlJsVariableList.push({
                                                    CtrlId: "con_164_47",
                                                    ResizeFunc: resize,
                                                });
                                            } else {
                                                $(window).resize(function (e) {
                                                    if (e.target == this) {
                                                        resize();
                                                    }
                                                });
                                            }

                                            resize();
                                        });
                                    </script>
                                </div>
                            </div>
                            <div id="smv_con_177_48" ctype="banner" class="esmartMargin smartAbs " cpid="60478"
                                cstyle="Style1" ccolor="Item0" areaid="Main" iscontainer="True" pvid="" tareaid="Main"
                                re-direction="y" daxis="Y" isdeletable="True"
                                style="height: 500px; width: 100%; left: 0px; top: 3184px;z-index:0;">
                                <div class="yibuFrameContent con_177_48  banner_Style1  " style="overflow:visible;;">
                                    <div class="fullcolumn-inner smAreaC" id="smc_Area0" cid="con_177_48"
                                        style="width:1200px">
                                        <div id="smv_con_178_48" ctype="area"
                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Left&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                            class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1"
                                            ccolor="Item0" areaid="Area0" iscontainer="True" pvid="con_177_48"
                                            tareaid="" re-direction="all" daxis="All" isdeletable="True"
                                            style="height: 338px; width: 560px; left: 20px; top: 81px; z-index: 1; opacity: 1;"
                                            sm-finished="true" smexecuted="1">
                                            <div class="yibuFrameContent con_178_48  area_Style1  "
                                                style="overflow:visible;;">
                                                <div class="w-container" data-effect-name="enterTop">
                                                    <div class="smAreaC" id="smc_Area0" cid="con_178_48">
                                                        <div id="smv_con_179_48" ctype="text"
                                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Left&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                                            class="esmartMargin smartAbs animated" cpid="60478"
                                                            cstyle="Style1" ccolor="Item0" areaid="Area0"
                                                            iscontainer="False" pvid="con_178_48" tareaid=""
                                                            re-direction="all" daxis="All" isdeletable="True"
                                                            style="height: 217px; width: 520px; left: 19px; top: 64px; z-index: 3; opacity: 1;"
                                                            sm-finished="true" smexecuted="1">
                                                            <div class="yibuFrameContent con_179_48  text_Style1  "
                                                                style="overflow:hidden;;">
                                                                <div id="txt_con_179_48" style="height: 100%;">
                                                                    <div class="editableContent" id="txtc_con_179_48"
                                                                        style="height: 100%; word-wrap:break-word;">
                                                                        <p style="text-align:justify"><span
                                                                                style="color:#004f9b"><strong><span
                                                                                        style="line-height:1.75"><span
                                                                                            style="font-size:22px">压风自动化系统</span></span></strong></span>
                                                                        </p>

                                                                        <p style="text-align:justify">&nbsp;</p>

                                                                        <p style="text-align:justify">&nbsp;</p>

                                                                        <p style="text-align:justify"><span
                                                                            style="line-height:1.75"><span
                                                                                style="font-size:16px">矿井压风机远程监控系统能够对压风系统的各管路状态进行自动监测，并进行数据处理，判断事故的原因及故障点，以便进行快速维修，对矿山安全建设具有重要意义。</span></span>
                                                                        </p>

                                                                    </div>
                                                                </div>

                                                                <script>
                                                                    var tables = $(' #smv_con_179_48').find('table')
                                                                    for (var i = 0; i < tables.length; i++) {
                                                                        var tab = tables[i]
                                                                        var borderWidth = $(tab).attr('border')
                                                                        if (borderWidth <= 0 || !borderWidth) {
                                                                            console.log(tab)
                                                                            $(tab).addClass('hidden-border')
                                                                            $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                                            $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                                            $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                                                        }
                                                                    }
                                                                </script>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="smv_con_180_48" ctype="image"
                                            smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Right&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}"
                                            class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1"
                                            ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_177_48"
                                            tareaid="" re-direction="all" daxis="All" isdeletable="True"
                                            style="height: 400px; width: 580px; left: 600px; top: 50px; z-index: 2; opacity: 1;"
                                            sm-finished="true" smexecuted="1">
                                            <div class="yibuFrameContent con_180_48  image_Style1  "
                                                style="overflow:visible;;">
                                                <div class="w-image-box image-clip-wrap" data-filltype="1"
                                                    id="div_con_180_48" style="height: 400px;">
                                                    <a target="_self">
                                                        <img loading="lazy" src="images/yfzdhxt.png"
                                                            alt="2d5c2da0-ba58-4d89-bb10-d21b429dd744" title=""
                                                            id="img_smv_con_180_48"
                                                            class="">
                                                    </a>
                                                </div>

                                                <script type="text/javascript">
                                                    $(function () {

                                                        //InitImageSmv("con_180_48", "578", "400", "1");

                                                    });
                                                </script>

                                            </div>
                                        </div>
                                    </div>
                                    <div id="bannerWrap_con_177_48" class="fullcolumn-outer"
                                        style="position: absolute; top: 0px; bottom: 0px; left: -292px; width: 1784px;">
                                    </div>

                                    <script type="text/javascript">

                                        $(function () {
                                            var resize = function () {
                                                $("#smv_con_177_48 >.yibuFrameContent>.fullcolumn-inner").width($("#smv_con_177_48").parent().width());
                                                $('#bannerWrap_con_177_48').fullScreen(function (t) {
                                                    if (VisitFromMobile()) {
                                                        t.css("min-width", t.parent().width())
                                                    }
                                                });
                                            }
                                            if (typeof (LayoutConverter) !== "undefined") {
                                                LayoutConverter.CtrlJsVariableList.push({
                                                    CtrlId: "con_177_48",
                                                    ResizeFunc: resize,
                                                });
                                            } else {
                                                $(window).resize(function (e) {
                                                    if (e.target == this) {
                                                        resize();
                                                    }
                                                });
                                            }

                                            resize();
                                        });
                                    </script>
                                </div>
                            </div>
                            <div id="smv_con_181_4" ctype="text" class="esmartMargin smartAbs " cpid="60478"
                                cstyle="Style1" ccolor="Item0" areaid="" iscontainer="False" pvid="" tareaid=""
                                re-direction="all" daxis="All" isdeletable="True"
                                style="height: 45px; width: 268px; left: 0px; top: 626px;z-index:1003;">
                                <div class="yibuFrameContent con_181_4  text_Style1  " style="overflow:hidden;;">
                                    <div id="txt_con_181_4" style="height: 100%;">
                                        <div class="editableContent" id="txtc_con_181_4"
                                            style="height: 100%; word-wrap:break-word;">
                                            <p><a id="1" name="1"></a></p>
                                        </div>
                                    </div>

                                    <script>
                                        var tables = $(' #smv_con_181_4').find('table')
                                        for (var i = 0; i < tables.length; i++) {
                                            var tab = tables[i]
                                            var borderWidth = $(tab).attr('border')
                                            if (borderWidth <= 0 || !borderWidth) {
                                                console.log(tab)
                                                $(tab).addClass('hidden-border')
                                                $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                            }
                                        }
                                    </script>
                                </div>
                            </div>


                            <div id="smv_con_172_19" ctype="banner" class="esmartMargin smartAbs " cpid="60478" cstyle="Style1" ccolor="Item0" areaid="Main" iscontainer="True" pvid="" tareaid="Main" re-direction="y" daxis="Y" isdeletable="True" style="height: 500px; width: 100%; left: 0px; top: 3656px;z-index:0;">
                                <div class="yibuFrameContent con_172_10  banner_Style1  " style="overflow:visible;;">
                                    <div class="fullcolumn-inner smAreaC" id="smc_Area0" cid="con_172_10" style="width:1200px">
                                        <div id="smv_con_174_10" ctype="area" smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Right&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}" class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="True" pvid="con_172_10" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 338px; width: 560px; left: 620px; top: 81px; z-index: 1; opacity: 1;" sm-finished="true" smexecuted="1">
                                            <div class="yibuFrameContent con_174_10  area_Style1  " style="overflow:visible;;">
                                                <div class="w-container" data-effect-name="enterTop">
                                                    <div class="smAreaC" id="smc_Area0" cid="con_174_10">
                                                        <div id="smv_con_175_10" ctype="text" smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Right&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}" class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_174_10" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 228px; width: 520px; left: 19px; top: 64px; z-index: 3; opacity: 1;" sm-finished="true" smexecuted="1">
                                                            <div class="yibuFrameContent con_175_10  text_Style1  " style="overflow:hidden;;">
                                                                <div id="txt_con_175_10" style="height: 100%;">
                                                                    <div class="editableContent" id="txtc_con_175_10" style="height: 100%; word-wrap:break-word;">
                                                                        <p style="text-align:justify"><span style="color:#004f9b"><strong><span style="line-height:1.75"><span style="font-size:22px">万兆环网集控系统</span></span></strong></span>
                                                                        </p>

                                                                        <p style="text-align:justify">&nbsp;</p>

                                                                        <p style="text-align:justify">&nbsp;</p>

                                                                        <p style="text-align:justify"><span style="line-height:1.75"><span style="font-size:16px">矿井万兆工业以太网传输平台是解决全矿井日益增长的数据传输要求和有限带宽相矛盾的有效手段，是构建智慧化矿山的基础硬件平台之一。</span></span>
                                                                        </p>

                                                                    </div>
                                                                </div>

                                                                <script>
                                                                    var tables = $(' #smv_con_172_19').find('table')
                                                                    for (var i = 0; i < tables.length; i++) {
                                                                        var tab = tables[i]
                                                                        var borderWidth = $(tab).attr('border')
                                                                        if (borderWidth <= 0 || !borderWidth) {
                                                                            console.log(tab)
                                                                            $(tab).addClass('hidden-border')
                                                                            $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                                            $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                                            $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                                            $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                                                        }
                                                                    }
                                                                </script>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="smv_con_173_10" ctype="image" smanim="{&quot;delay&quot;:0.75,&quot;duration&quot;:0.75,&quot;direction&quot;:&quot;Left&quot;,&quot;animationName&quot;:&quot;slideIn&quot;,&quot;infinite&quot;:&quot;1&quot;}" class="esmartMargin smartAbs animated" cpid="60478" cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_172_10" tareaid="" re-direction="all" daxis="All" isdeletable="True" style="height: 400px; width: 580px; left: 20px; top: 50px; z-index: 2; opacity: 1;" sm-finished="true" smexecuted="1">
                                            <div class="yibuFrameContent con_173_10  image_Style1  " style="overflow:visible;;">
                                                <div class="w-image-box image-clip-wrap" data-filltype="1" id="div_con_173_10" style="height: 400px;">
                                                    <a target="_self">
                                                        <img loading="lazy" src="images/wzhwzdhxt.png" alt="fd770139-5ebf-41c0-a431-74ef667b08d8" title="" id="img_smv_con_173_10" class="">
                                                    </a>
                                                </div>

                                                <script type="text/javascript">
                                                    $(function () {

                                                        //InitImageSmv("con_173_10", "578", "400", "1");

                                                    });
                                                </script>

                                            </div>
                                        </div>
                                        <div id="smv_con_172_19" ctype="text" class="esmartMargin smartAbs " cpid="60478" cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="con_172_10" tareaid="Main" re-direction="all" daxis="All" isdeletable="True" style="height: 30px; width: 360px; left: 14px; top: 459px;z-index:4;">
                                            <div class="yibuFrameContent con_186_19  text_Style1  " style="overflow:hidden;;">
                                                <div id="txt_con_186_19" style="height: 100%;">
                                                    <div class="editableContent" id="txtc_con_186_19" style="height: 100%; word-wrap:break-word;">
                                                        <p><a id="6" name="6"></a></p>

                                                    </div>
                                                </div>

                                                <script>
                                                    var tables = $(' #smv_con_172_19').find('table')
                                                    for (var i = 0; i < tables.length; i++) {
                                                        var tab = tables[i]
                                                        var borderWidth = $(tab).attr('border')
                                                        if (borderWidth <= 0 || !borderWidth) {
                                                            console.log(tab)
                                                            $(tab).addClass('hidden-border')
                                                            $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                            $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                            $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                            $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                                        }
                                                    }
                                                </script>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="bannerWrap_con_172_10" class="fullcolumn-outer" style="position: absolute; top: 0px; bottom: 0px; left: -292px; width: 1784px;">
                                    </div>

                                    <script type="text/javascript">

                                        $(function () {
                                            var resize = function () {
                                                $("#smv_con_172_19 >.yibuFrameContent>.fullcolumn-inner").width($("#smv_con_172_19").parent().width());
                                                $('#bannerWrap_con_172_10').fullScreen(function (t) {
                                                    if (VisitFromMobile()) {
                                                        t.css("min-width", t.parent().width())
                                                    }
                                                });
                                            }
                                            if (typeof (LayoutConverter) !== "undefined") {
                                                LayoutConverter.CtrlJsVariableList.push({
                                                    CtrlId: "con_172_10",
                                                    ResizeFunc: resize,
                                                });
                                            } else {
                                                $(window).resize(function (e) {
                                                    if (e.target == this) {
                                                        resize();
                                                    }
                                                });
                                            }

                                            resize();
                                        });
                                    </script>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="background-color: rgb(33, 37, 46); background-image: none; background-repeat: no-repeat;background-position:0 0; background:-moz-linear-gradient(top, none, none);background:-webkit-gradient(linear, left top, left bottom, from(none), to(none));background:-o-linear-gradient(top, none, none);background:-ms-linear-gradient(top, none, none);background:linear-gradient(top, none, none);;
             position: relative; width: 100%;min-width:1200px;background-size: auto;" bgscroll="none">
            <div class=" footer" cpid="60135" id="smv_Area3"
                style="width: 1200px; height: 130px; position: relative; margin: 0 auto;">
                <div id="smv_tem_5_11" ctype="banner" class="esmartMargin smartAbs " cpid="60135" cstyle="Style1"
                    ccolor="Item0" areaid="Area3" iscontainer="True" pvid="" tareaid="Area3" re-direction="y" daxis="Y"
                    isdeletable="True" style="height: 130px; width: 100%; left: 0px; top: 0px;z-index:2;">
                    <div class="yibuFrameContent tem_5_11  banner_Style1  " style="overflow:visible;;">
                        <div class="fullcolumn-inner smAreaC" id="smc_Area0" cid="tem_5_11" style="width:1200px">
                            <div id="smv_tem_35_16" ctype="text" class="esmartMargin smartAbs " cpid="60135"
                                cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="tem_5_11"
                                tareaid="Area3" re-direction="all" daxis="All" isdeletable="True"
                                style="height: 84px; width: 415px; left: 43px; top: 29px;z-index:11;">
                                <div class="yibuFrameContent tem_35_16  text_Style1  " style="overflow:hidden;;">
                                    <div id="txt_tem_35_16" style="height: 100%;">
                                        <div class="editableContent" id="txtc_tem_35_16"
                                            style="height: 100%; word-wrap:break-word;">
                                            <p><span style="line-height:2"><span style="color:#ffffff"><span
                                                            style="font-family:Microsoft YaHei"><span
                                                                style="font-size:14px">邮箱：<EMAIL>&nbsp; 电话：李经理14752221901<br>
                                                                徐州高新技术产业开发区漓江路15号徐州国家安全科技产业园B5#-1-205</span></span></span></span>
                                            </p>

                                        </div>
                                    </div>

                                    <script>
                                        var tables = $(' #smv_tem_35_16').find('table')
                                        for (var i = 0; i < tables.length; i++) {
                                            var tab = tables[i]
                                            var borderWidth = $(tab).attr('border')
                                            if (borderWidth <= 0 || !borderWidth) {
                                                console.log(tab)
                                                $(tab).addClass('hidden-border')
                                                $(tab).children("tbody").children("tr").children("td").addClass('hidden-border')
                                                $(tab).children("tbody").children("tr").children("th").addClass('hidden-border')
                                                $(tab).children("thead").children("tr").children("td").addClass('hidden-border')
                                                $(tab).children("thead").children("tr").children("th").addClass('hidden-border')
                                                $(tab).children("tfoot").children("tr").children("td").addClass('hidden-border')
                                                $(tab).children("tfoot").children("tr").children("th").addClass('hidden-border')
                                            }
                                        }
                                    </script>
                                </div>
                            </div>
                        </div>
                        <div id="bannerWrap_tem_5_11" class="fullcolumn-outer"
                            style="position: absolute; top: 0px; bottom: 0px; left: -292px; width: 1784px;">
                        </div>

                        <script type="text/javascript">

                            $(function () {
                                var resize = function () {
                                    $("#smv_tem_5_11 >.yibuFrameContent>.fullcolumn-inner").width($("#smv_tem_5_11").parent().width());
                                    $('#bannerWrap_tem_5_11').fullScreen(function (t) {
                                        if (VisitFromMobile()) {
                                            t.css("min-width", t.parent().width())
                                        }
                                    });
                                }
                                if (typeof (LayoutConverter) !== "undefined") {
                                    LayoutConverter.CtrlJsVariableList.push({
                                        CtrlId: "tem_5_11",
                                        ResizeFunc: resize,
                                    });
                                } else {
                                    $(window).resize(function (e) {
                                        if (e.target == this) {
                                            resize();
                                        }
                                    });
                                }

                                resize();
                            });
                        </script>
                    </div>
                </div>
                <div id="smv_tem_36_17" ctype="image" class="esmartMargin smartAbs " cpid="60135" cstyle="Style1"
                    ccolor="Item0" areaid="Area3" iscontainer="False" pvid="" tareaid="Area3" re-direction="all"
                    daxis="All" isdeletable="True" style="height: 25px; width: 25px; left: 10px; top: 30px;z-index:12;">
                    <div class="yibuFrameContent tem_36_17  image_Style1  " style="overflow:visible;;">
                        <div class="w-image-box image-clip-wrap" data-filltype="1" id="div_tem_36_17"
                            style="height: 25px;">
                            <a target="_self">
                                <img loading="lazy" src="images/15444447.png"
                                    alt="邮箱" title="" id="img_smv_tem_36_17"
                                    style="width: auto; height: 25px; margin-left: -1px; margin-top: 0px;" class="">
                            </a>
                        </div>

                        <script type="text/javascript">
                            $(function () {

                                InitImageSmv("tem_36_17", "23", "25", "1");

                            });
                        </script>

                    </div>
                </div>
                <div id="smv_tem_37_58" ctype="image" class="esmartMargin smartAbs " cpid="60135" cstyle="Style1"
                    ccolor="Item0" areaid="Area3" iscontainer="False" pvid="" tareaid="Area3" re-direction="all"
                    daxis="All" isdeletable="True" style="height: 25px; width: 25px; left: 10px; top: 58px;z-index:12;">
                    <div class="yibuFrameContent tem_37_58  image_Style1  " style="overflow:visible;;">
                        <div class="w-image-box image-clip-wrap" data-filltype="0" id="div_tem_37_58"
                            style="height: 25px;">
                            <a target="_self">
                                <img loading="lazy" src="images/15444503.png"
                                    alt="定位" title="" id="img_smv_tem_37_58" style="width: 23px; height: 25px;"
                                    class="">
                            </a>
                        </div>

                        <script type="text/javascript">
                            $(function () {

                                InitImageSmv("tem_37_58", "23", "25", "0");

                            });
                        </script>

                    </div>
                </div>
                <div id="smv_tem_40_31" ctype="code" class="esmartMargin smartAbs " cpid="60135" cstyle="Style1"
                    ccolor="Item0" areaid="Area3" iscontainer="False" pvid="" tareaid="Area3" re-direction="all"
                    daxis="All" isdeletable="True" style="height: 20px; width: 95px; left: 836px; top: 23px;z-index:0;">
                    <div class="yibuFrameContent tem_40_31  code_Style1  " style="overflow:hidden;;">
                        <!--w-code-->
                        <div class="w-code">
                            <div id="code_tem_40_31">
                                <script>
                                    setTimeout(function () {
                                        $("img").attr("title", "")
                                    }, 10)
                                </script>
                            </div>
                        </div>
                        <!--/w-code-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        xwezhan.initWz();
    </script>

    <script type="text/javascript">
        $(document.body).bind('contextmenu', function () { return false; });                
    </script>

    <div id="smv_tem_13_34" ctype="qqservice" class="esmartMargin smartAbs smartFixed    exist" cpid="60135"
        cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="" tareaid="Area0" re-direction="x"
        daxis="All" isdeletable="True" style="height: 61px; width: 100px; right: 0px; bottom: 0px;z-index:6;">
        <div class="yibuFrameContent tem_13_34  qqservice_Style1  " style="overflow:hidden;;">
            <div class="tem_13_34_c w-cs" id="qqservice_tem_13_34">
                <ul class="w-cs-btn">
                    <li class="w-cs-list w-cs-upBtn ">
                        <a href="javascript:void(0);" onclick="gotoTop();return false;" class="w-cs-icon"><i
                                class="iconfont icon-direction-up"></i></a>
                    </li>
                </ul>
            </div>
            <!--/w-cs-->
            <script>

                $(function () {
                    var sv = $("#qqservice_tem_13_34");

                    var numbers = [];
                    $.each(sv.find(".w-cs-menu"), function () { numbers.push(this.scrollWidth); });
                    var maxInNumbers = Math.max.apply(Math, numbers);

                    sv.find(".w-cs-menu").css("width", maxInNumbers + "px");
                    //  显示
                    sv.find(".w-cs-list").hover(function () {
                        $(this).find("ul.w-cs-menu").stop().animate({ right: 61 }, 200);
                    }, function () {
                        $(this).find("ul.w-cs-menu").stop().animate({ right: "0" }, 200);
                    });

                    $("#smv_tem_13_34").addClass('exist').appendTo($('body'));

                });
                function gotoTop(acceleration, stime) {
                    acceleration = acceleration || 0.1;
                    stime = stime || 10;
                    var x1 = 0;
                    var y1 = 0;
                    var x2 = 0;
                    var y2 = 0;
                    if (document.documentElement) {
                        x1 = document.documentElement.scrollLeft || 0;
                        y1 = document.documentElement.scrollTop || 0;
                    }
                    if (document.body) {
                        x2 = document.body.scrollLeft || 0;
                        y2 = document.body.scrollTop || 0;
                    }
                    var x3 = window.scrollX || 0;
                    var y3 = window.scrollY || 0;

                    // 滚动条到页面顶部的水平距离
                    var x = Math.max(x1, Math.max(x2, x3));
                    // 滚动条到页面顶部的垂直距离
                    var y = Math.max(y1, Math.max(y2, y3));

                    // 滚动距离 = 目前距离 / 速度, 因为距离原来越小, 速度是大于 1 的数, 所以滚动距离会越来越小
                    var speeding = 1 + acceleration;
                    window.scrollTo(Math.floor(x / speeding), Math.floor(y / speeding));

                    // 如果距离不为零, 继续调用函数
                    if (x > 0 || y > 0) {
                        var run = "gotoTop(" + acceleration + ", " + stime + ")";
                        window.setTimeout(run, stime);
                    }

                    if (typeof (LayoutConverter) !== "undefined" && typeof (CtrlAdjuster) !== "undefined" && CtrlAdjuster.IsMobile) {
                        $("#qqservice_tem_13_34").trigger("mouseout");
                    }
                }
            </script>
        </div>
    </div>
</body>

</html>